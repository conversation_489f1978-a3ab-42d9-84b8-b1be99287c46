name: Bug report
description: File a bug report
labels: [bug]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to report a bug!
  - type: input
    id: summary
    attributes:
      label: Summary
      description: Short description of the issue
      placeholder: A clear and concise description
    validations:
      required: true
  - type: textarea
    id: steps
    attributes:
      label: Steps to reproduce
      description: How do we reproduce the bug?
      placeholder: |
        1. Go to ...
        2. Click ...
        3. See error ...
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: Expected behavior
    validations:
      required: true
  - type: textarea
    id: actual
    attributes:
      label: Actual behavior / logs
      description: Include screenshots and console logs if possible
  - type: input
    id: env
    attributes:
      label: Environment
      placeholder: Windows 11 / PHP 8.2 / Node 20
  - type: checkboxes
    id: checks
    attributes:
      label: Checks
      options:
        - label: I searched existing issues
        - label: This is not a duplicate
