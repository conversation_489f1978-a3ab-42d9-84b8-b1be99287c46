## Summary

Describe the change and why it’s needed.

## Screenshots (UI changes)

Add before/after screenshots or GIFs (light + dark mode).

## How to Test

1. ...
2. ...

## Checklist

- [ ] Builds and runs locally (`php artisan serve`, `npm run dev`)
- [ ] Migrations run successfully
- [ ] Style/lint passes (`vendor/bin/pint`)
- [ ] No secrets or env files committed
- [ ] Added/updated docs if behavior changed
- [ ] Screenshots provided for UI changes
