/.phpunit.cache
/node_modules
/public/build
/public/hot
/public/storage
/storage/*.key
/storage/pail
/storage/framework/cache
/storage/framework/cache/data
/storage/framework/sessions
/storage/framework/testing
/storage/framework/views
/storage/logs
/storage/debugbar
/vendor
.env
.env.backup
.env.production
.env.local
.env.development
.env.staging
.env.testing
.env.test
.phpactor.json
.phpunit.result.cache
Homestead.json
Homestead.yaml
npm-debug.log*
yarn-debug.log*
yarn-error.log*
/auth.json
/.fleet
/.idea
/.nova
/.vscode
/.zed

# Laravel cache artifacts
/bootstrap/cache/*.php
/bootstrap/cache/*.json
!/bootstrap/cache/.gitignore

# Coverage reports
/coverage/

# OS-generated files
.DS_Store
Thumbs.db

# Editors/formatters
.php-cs-fixer.cache

# Local databases (SQLite)
/database/*.sqlite
/database/*.sqlite3
