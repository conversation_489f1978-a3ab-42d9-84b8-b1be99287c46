# Contributing Guide

Thanks for considering a contribution! This guide explains how to set up the project locally, coding standards, and how to submit a pull request.

## Local Setup (Windows PowerShell)

1. Clone and install dependencies

```powershell
git clone <your-fork-or-origin-url>.git
cd projet_laravel
composer install
npm install
```

2. Environment

```powershell
copy .env.example .env
php artisan key:generate
# SQLite DB (recommended for dev)
if (!(Test-Path .\database\database.sqlite)) { New-Item .\database\database.sqlite -ItemType File | Out-Null }
# Set JWT secret in .env (or export JWT_SECRET for process)
```

3. Database

```powershell
php artisan migrate
php artisan db:seed
```

4. Run

```powershell
php artisan serve
npm run dev
```

Open http://127.0.0.1:8000.

Seeded test user: `<EMAIL>` / `secret`

## Coding Standards

- PHP: follow PSR-12; run Pint before committing

```powershell
./vendor/bin/pint
```

- JS/CSS: keep styles consistent with existing variables in `resources/css/app.css`
- Avoid committing built assets or vendor directories; `.gitignore` handles most cases

## Branching & Commits

- Create feature branches from `ahmed`: `feat/<short-title>`; for fixes: `fix/<short-title>`
- Write clear commit messages (imperative): `feat(auth): add two-step reset`

## Tests

- Run unit/feature tests locally when relevant

```powershell
php artisan test
```

## Pull Request Checklist

- [ ] The app builds and runs locally (PHP + Vite)
- [ ] Migrations run successfully
- [ ] Lint/style passes (`vendor/bin/pint`)
- [ ] No secrets or environment files committed
- [ ] Updated docs (README/CONTRIBUTING) if behavior changed
- [ ] Screenshots/GIFs for UI changes (light/dark, mobile/desktop)

## Reporting Issues

Use the issue templates and include steps to reproduce, expected vs actual, screenshots, and logs when possible.
