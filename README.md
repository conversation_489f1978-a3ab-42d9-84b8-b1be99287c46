<h1 align="center">Healthcare Tracker – Laravel + Blade</h1>

All-in-one personal health tracker with themeable UI, daily vitals, Blade dashboard, profile with photo upload, secure two-step password reset via email code, and JWT-protected API.

## Quick Start (Windows PowerShell)

1) Install dependencies

```powershell
composer install
npm install
```

2) Environment setup

```powershell
copy .env.example .env
php artisan key:generate

# Database (SQLite is used by default)
if (!(Test-Path .\database\database.sqlite)) { New-Item .\database\database.sqlite -ItemType File | Out-Null }

# JWT secret (required for API auth)
[System.Environment]::SetEnvironmentVariable('JWT_SECRET',(New-Guid).Guid,'Process')
# Or put JWT_SECRET in .env manually
```

3) Migrate and seed

```powershell
php artisan migrate
php artisan db:seed
```

4) Run the app (two terminals recommended)

```powershell
# Terminal 1
php artisan serve

# Terminal 2
npm run dev
```

Open http://127.0.0.1:8000 in your browser.

Seeded test user: `<EMAIL>` / `secret`

## Features

- Dark/Light theme toggle (global UI variables)
- Onboarding with daily-mode vitals (one entry/day in Africa/Tunis timezone)
- Blade-only Dashboard with 24h live series, 7-day aggregates, trends, export CSV
- Profile page with avatar upload (public storage) and full vitals history with filters
- Email-based password reset with two-step UX (verify code → set new password)
- JWT-authenticated API with login/me/refresh/logout and protected vitals feed

## Theming

The UI uses CSS variables defined in `resources/css/app.css`.

- Light mode: `--color-text` is dark; Dark mode: `--color-text` is light
- Inputs and cards use `--color-surface` / `--color-surface-alt` and `--color-border`

All new auth views (verify code, new password, success) are fully adapted to dark/light via these variables.

## Data & Timezone Policy

- Daily vitals enforce one row per `patient_id` and `recorded_on` in Africa/Tunis
- Timestamps stored in UTC; `recorded_on` computed server-side by the policy
- Dashboard shows a reminder modal after noon local time if no entry exists for today

## Dashboard & Profile

- Dashboard: current vitals, live 24h series, 7-day aggregates, sparklines, download CSV
- Profile: photo upload (public disk) + timeline of all vitals with filters and pagination

## Password Reset (Two-step)

1) Forgot: enter email → we send a 6-digit code (TTL 5 minutes, throttle + daily quota)
2) Verify: enter code → if valid, session marks email verified
3) New password: set and confirm → token cleared, success page with animation

Security extras:
- Code hashed in `password_reset_tokens`
- Throttling, attempt limits, daily quotas, and audit logging (best-effort)
- Success page at `/password/success` with animated check and auto-redirect to login

## JWT API

Package: `firebase/php-jwt` (HS256) with custom `JwtService` and middleware.

Config: `config/jwt.php` – set `JWT_SECRET` in `.env`.

Routes:
- `POST /api/v1/auth/login` → returns `{ token, exp }`
- `GET /api/v1/auth/me` (JWT)
- `POST /api/v1/auth/refresh` (JWT)
- `POST /api/v1/auth/logout` (JWT, denylist by `jti`)
- `GET /api/v1/vitals/feed` (JWT)

Web extras:
- `GET /vitals/feed` (session auth, JSON for dashboard)
- `GET /dashboard/export/csv` (session auth)

## Email

Configure SMTP in `.env` (e.g., Gmail). The password reset mailable is queue-ready; in development it sends synchronously.

## Storage

Profile avatars are stored on the `public` disk. Ensure the symlink exists:

```powershell
php artisan storage:link
```

## Troubleshooting

- 401 on API login: ensure `JWT_SECRET` is set and re-login to get a new token
- SQL errors for `password_reset_attempts`: run migrations; controller guards best-effort logging if table is missing
- Password reset code not received: check SMTP config or use a dev mail catcher
- Dark/light styles not applied: ensure `@vite(['resources/css/app.css','resources/js/app.js'])` is present in Blade and no CSS caching issues (try hard refresh)

## Scripts & Dev

Handy dev script in `composer.json`:

```powershell
composer run dev
```

This runs: PHP server, queue listener, live logs, and Vite.

---

MIT License. Built on Laravel 12 with Blade UI.
