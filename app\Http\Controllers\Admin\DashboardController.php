<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Patient;
use App\Models\Doctor;
use App\Models\Vital;

class DashboardController extends Controller
{
    public function index()
    {
        return view('admin.dashboard', [
            'counts' => [
                'users' => User::count(),
                'patients' => Patient::count(),
                'doctors' => Doctor::count(),
                'vitals' => Vital::count(),
            ],
        ]);
    }
}
