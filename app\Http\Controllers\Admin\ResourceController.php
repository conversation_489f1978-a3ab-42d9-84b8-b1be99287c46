<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;

class ResourceController extends Controller
{
    protected array $map = [
        'users' => \App\Models\User::class,
        'patients' => \App\Models\Patient::class,
        'doctors' => \App\Models\Doctor::class,
        'vitals' => \App\Models\Vital::class,
        'appointments' => \App\Models\Appointment::class,
        'medications' => \App\Models\Medication::class,
        'alerts' => \App\Models\Alert::class,
        'ml-predictions' => \App\Models\MlPrediction::class,
        'logs' => \App\Models\ActivityLog::class,
        'reports' => \App\Models\Report::class,
        'ordonnances' => \App\Models\Ordonnance::class,
        'tasks' => \App\Models\Task::class,
        'lifestyles' => \App\Models\Lifestyle::class,
        'mental-health' => \App\Models\MentalHealth::class,
        'articles' => \App\Models\Article::class,
        'forum-posts' => \App\Models\ForumPost::class,
    ];

    protected function resolveModel(string $resource)
    {
        $model = $this->map[$resource] ?? null;
        abort_unless($model, 404);
        return $model;
    }

    /**
     * Columns to display in index table per resource (hide IDs / credentials).
     */
    protected function visibleColumns(string $resource, $firstRow): array
    {
        $byResource = [
            'users' => ['name','email','role','email_verified_at','created_at'],
            'patients' => ['date_of_birth','gender','phone_number','address','insurance_provider'],
            'doctors' => ['specialty','license_number','hospital_affiliation'],
        ];
        if (isset($byResource[$resource])) {
            return array_values(array_intersect($byResource[$resource], array_keys($firstRow->getAttributes())));
        }
        // Generic: show all except typical IDs or secrets
        $hidden = ['password','password_hash','remember_token','verification_code','verification_attempts','verification_code_expires_at','last_verification_sent_at','user_id','patient_id','doctor_id'];
        return array_values(array_diff(array_keys($firstRow->getAttributes()), $hidden));
    }

    /**
     * Allowed form fields per resource (hide secrets; keep only admin-relevant).
     */
    protected function allowedFields(string $resource, $instance): array
    {
        if ($resource === 'users') {
            // Admin can manage these; password handled separately
            $allow = ['name','email','role','photo_url','theme_preference'];
            return array_values(array_intersect($allow, $instance->getFillable()));
        }
        if ($resource === 'patients') {
            // Keep user_id for association; omit IDs or internal
            $allow = ['user_id','date_of_birth','gender','address','phone_number','emergency_contact','insurance_provider','policy_number','health_history','height_cm','allergies','medical_conditions'];
            return array_values(array_intersect($allow, $instance->getFillable()));
        }
        if ($resource === 'doctors') {
            $allow = ['user_id','specialty','license_number','hospital_affiliation'];
            return array_values(array_intersect($allow, $instance->getFillable()));
        }
        // Default: all fillable minus typical secrets
        $hidden = ['password','password_hash','remember_token','verification_code','verification_attempts','verification_code_expires_at','last_verification_sent_at'];
        return array_values(array_diff($instance->getFillable(), $hidden));
    }

    public function index(Request $request, string $resource)
    {
        $model = $this->resolveModel($resource);
        $query = $model::query();

        // Basic search by 'q' across fillable string columns
        if ($q = $request->input('q')) {
            $instance = new $model;
            $fillable = method_exists($instance, 'getFillable') ? $instance->getFillable() : [];
            $query->where(function($w) use ($fillable, $q) {
                foreach ($fillable as $col) {
                    $w->orWhere($col, 'like', "%$q%");
                }
            });
        }

        $keyName = (new $model)->getKeyName() ?: 'id';
        $items = $query->orderByDesc($keyName)->paginate(15);
        $first = $items->first();
        $columns = $first ? $this->visibleColumns($resource, $first) : [];
        return view('admin.resource.index', compact('items', 'resource', 'columns'));
    }

    public function create(string $resource)
    {
        $model = $this->resolveModel($resource);
        $instance = new $model;
        $fields = $this->allowedFields($resource, $instance);
        return view('admin.resource.form', [
            'resource' => $resource,
            'item' => $instance,
            'isCreate' => true,
            'fields' => $fields,
        ]);
    }

    public function store(Request $request, string $resource)
    {
        $model = $this->resolveModel($resource);
        $instance = new $model;
        $fields = $this->allowedFields($resource, $instance);
        $data = $request->only($fields);
        // Special handling for users: optional password
        if ($resource === 'users' && $request->filled('password')) {
            $data['password_hash'] = Hash::make($request->input('password'));
        }
        $item = $model::create($data);
        return redirect()->route('admin.resource.edit', [$resource, $item->getKey()])
            ->with('status', Str::headline($resource).' created');
    }

    public function edit(string $resource, $id)
    {
        $model = $this->resolveModel($resource);
        $item = $model::findOrFail($id);
        $instance = new $model;

        $fields = $this->allowedFields($resource, $instance);
        return view('admin.resource.form', [
            'resource' => $resource,
            'item' => $item,
            'isCreate' => false,
            'fields' => $fields,
        ]);
    }

    public function update(Request $request, string $resource, $id)
    {
        $model = $this->resolveModel($resource);
        $item = $model::findOrFail($id);
        $fields = $this->allowedFields($resource, $item);
        $data = $request->only($fields);
        if ($resource === 'users' && $request->filled('password')) {
            $data['password_hash'] = Hash::make($request->input('password'));
        }
        $item->fill($data)->save();
        return back()->with('status', Str::headline($resource).' updated');
    }

    public function destroy(string $resource, $id)
    {
        $model = $this->resolveModel($resource);
        $item = $model::findOrFail($id);
        $item->delete();
        return redirect()->route('admin.resource.index', [$resource])->with('status', 'Deleted');
    }
}
