<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\JwtService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class AuthController extends Controller
{
    public function login(Request $request)
    {
        $data = $request->validate([
            'email' => ['required','email'],
            'password' => ['required','string'],
        ]);
        /** @var User|null $user */
        $user = User::where('email', $data['email'])->first();
        if (!$user || !Hash::check($data['password'], $user->getAuthPassword())) {
            return response()->json(['message' => 'Invalid credentials'], 401);
        }
        $token = JwtService::issue([
            'sub' => $user->user_id,
            'email' => $user->email,
            'role' => $user->role,
        ]);
        return response()->json(['access_token' => $token, 'token_type' => 'Bearer']);
    }

    public function me(Request $request)
    {
        $u = $request->user();
        if (!$u) return response()->json(['message' => 'Unauthorized'], 401);
        return response()->json([
            'user_id' => $u->user_id,
            'name' => $u->name,
            'email' => $u->email,
            'role' => $u->role,
        ]);
    }

    public function refresh(Request $request)
    {
        $u = $request->user();
        if (!$u) return response()->json(['message' => 'Unauthorized'], 401);
        $claims = $request->attributes->get('jwt_claims', []);
        // Optional: ensure within refresh window
        $iat = (int) ($claims['iat'] ?? 0);
        $refreshTtl = (int) config('jwt.refresh_ttl', 20160); // minutes
        if ($iat > 0 && (time() - $iat) > ($refreshTtl * 60)) {
            return response()->json(['message' => 'Token expired, please login again'], 401);
        }
        $token = JwtService::issue([
            'sub' => $u->user_id,
            'email' => $u->email,
            'role' => $u->role,
        ]);
        return response()->json(['access_token' => $token, 'token_type' => 'Bearer']);
    }

    public function logout(Request $request)
    {
        $claims = $request->attributes->get('jwt_claims', []);
        $token = $request->attributes->get('jwt_token');
        $exp = (int) ($claims['exp'] ?? (time()+3600));
        $jti = $claims['jti'] ?? null;
        // Put jti on denylist until natural expiry
        if ($jti && function_exists('cache')) {
            $ttlSeconds = max(0, $exp - time());
            cache()->put('jwt:blacklist:' . $jti, true, $ttlSeconds);
        }
        return response()->json(['message' => 'Logged out']);
    }
}
