<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Patient;
use App\Models\Doctor;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Carbon;
use App\Mail\VerifyEmailOtp;

class AuthController extends Controller
{
    /**
     * Display the login page.
     * NOTE: Authentication handling, credential validation, and session creation
     * will be implemented later. Currently purely presentational.
     */
    public function showLogin()
    {
        return view('auth.login');
    }

    /**
     * Display the registration page.
     * NOTE: Future implementation will persist user, hash password, and store
     * role-specific attributes (patient vs doctor) in a dedicated users table and
     * potentially normalized related tables (e.g. patient_profiles / doctor_profiles).
     */
    public function showRegister()
    {
        return view('auth.register');
    }

    /**
     * Handle an incoming registration request.
     */
    public function register(Request $request)
    {
        $data = $request->validate([
            'name' => ['required','string','max:255'],
            'email' => ['required','string','email','max:255','unique:users,email'],
            'password' => ['required','confirmed', Password::defaults()],
            'role' => ['required','in:patient,doctor'],
            // Patient optional fields
            'dob' => ['nullable','date'],
            'gender' => ['nullable','string','max:15'],
            'address' => ['nullable','string','max:255'],
            'phone' => ['nullable','string','max:30'],
            'emergency_contact' => ['nullable','string','max:255'],
            'insurance_provider' => ['nullable','string','max:255'],
            'policy_number' => ['nullable','string','max:100'],
            'health_history' => ['nullable','string'],
            // Doctor optional fields
            'specialty' => ['nullable','string','max:255'],
            'license_number' => ['nullable','string','max:100'],
            'hospital' => ['nullable','string','max:255'],
        ]);

        $user = User::create([
            'name' => $data['name'],
            'email' => $data['email'],
            'password_hash' => Hash::make($data['password']),
            'role' => $data['role'],
        ]);

        if ($data['role'] === 'patient') {
            Patient::create([
                'user_id' => $user->user_id,
                'date_of_birth' => $data['dob'] ?? null,
                'gender' => $data['gender'] ?? null,
                'address' => $data['address'] ?? null,
                'phone_number' => $data['phone'] ?? null,
                'emergency_contact' => $data['emergency_contact'] ?? null,
                'insurance_provider' => $data['insurance_provider'] ?? null,
                'policy_number' => $data['policy_number'] ?? null,
                'health_history' => $data['health_history'] ?? null,
            ]);
        } elseif ($data['role'] === 'doctor') {
            Doctor::create([
                'user_id' => $user->user_id,
                'specialty' => $data['specialty'] ?? null,
                'license_number' => $data['license_number'] ?? null,
                'hospital_affiliation' => $data['hospital'] ?? null,
            ]);
        }

    // Generate OTP and send email
    $code = (string) random_int(100000, 999999);
    $user->verification_code = $code;
    $user->verification_code_expires_at = now()->addMinutes(10);
    $user->verification_attempts = 0;
    $user->last_verification_sent_at = now();
    $user->save();

    Mail::to($user->email)->send(new VerifyEmailOtp($user->name, $code, 10));

        Auth::login($user);

        return redirect()->route('verification.otp.form')
            ->with('status', 'We sent you a 6-digit code to verify your email.');
    }

    /**
     * Attempt to authenticate the user.
     */
    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => ['required','email'],
            'password' => ['required'],
        ]);

        if (Auth::attempt($credentials, $request->boolean('remember'))) {
            $request->session()->regenerate();
            // If email not verified, push to OTP form
            if (! $request->user()->hasVerifiedEmail()) {
                return redirect()->route('verification.otp.form');
            }
            if (! $request->user()->hasBaselineVitals()) {
                return redirect()->route('onboarding.welcome');
            }
            $fallback = $request->user()->role === 'admin'
                ? route('admin.dashboard')
                : route('dashboard');
            return redirect()->intended($fallback);
        }

        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ])->onlyInput('email');
    }

    /**
     * Log the user out.
     */
    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect('/');
    }

    /** Show OTP input form */
    public function showOtpForm(Request $request)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }
        if ($request->user()->hasVerifiedEmail()) {
            $fallback = $request->user()->role === 'admin'
                ? route('admin.dashboard')
                : route('dashboard');
            return redirect()->intended($fallback);
        }
        return view('auth.verify-otp');
    }

    /** Verify submitted OTP */
    public function verifyOtp(Request $request)
    {
        $request->validate([
            'code' => ['required','digits:6'],
        ]);
        $user = $request->user();
        if (!$user) {
            return redirect()->route('login');
        }

        if (!$user->verification_code || !$user->verification_code_expires_at) {
            return back()->withErrors(['code' => 'No active verification code. Please request a new one.']);
        }
        if (now()->greaterThan($user->verification_code_expires_at)) {
            return back()->withErrors(['code' => 'This code has expired. Please request a new one.']);
        }
        if (hash_equals((string) $user->verification_code, (string) $request->string('code'))) {
            $user->email_verified_at = now();
            $user->verification_code = null;
            $user->verification_code_expires_at = null;
            $user->verification_attempts = 0;
            $user->save();

            if (!$user->hasBaselineVitals()) {
                return redirect()->route('onboarding.welcome')->with('verified', true);
            }
            $target = $user->role === 'admin' ? 'admin.dashboard' : 'dashboard';
            return redirect()->route($target)->with('verified', true);
        }

        $user->increment('verification_attempts');
        return back()->withErrors(['code' => 'Invalid code. Please try again.']);
    }

    /** Resend OTP with throttle */
    public function resendOtp(Request $request)
    {
        $user = $request->user();
        if (!$user) {
            return redirect()->route('login');
        }
        if ($user->hasVerifiedEmail()) {
            return redirect()->route('dashboard');
        }
        // Simple throttle: 1 email per 60 seconds
        if ($user->last_verification_sent_at && now()->diffInSeconds($user->last_verification_sent_at) < 60) {
            $wait = 60 - now()->diffInSeconds($user->last_verification_sent_at);
            return back()->with('status', "Please wait {$wait}s before requesting another code.");
        }
        $code = (string) random_int(100000, 999999);
        $user->verification_code = $code;
        $user->verification_code_expires_at = now()->addMinutes(10);
        $user->verification_attempts = 0;
        $user->last_verification_sent_at = now();
        $user->save();
        Mail::to($user->email)->send(new VerifyEmailOtp($user->name, $code, 10));
        return back()->with('status', 'A new verification code has been sent.');
    }
}
