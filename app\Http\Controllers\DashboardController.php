<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\StreamedResponse;
use App\Models\Vital;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        $patient = $user->patient;
        $current = null;
        $series24h = [];
        $series7d = [];
        $summary = [
            'status' => 'No data',
            'color' => '#64748b',
            'note' => 'Add vitals to see insights.',
        ];

        if ($patient) {
            $vitals = Vital::where('patient_id', $patient->patient_id)
                ->where('recorded_at', '>=', now()->subDays(7))
                ->orderBy('recorded_at', 'asc')
                ->get();

            $current = Vital::where('patient_id', $patient->patient_id)
                ->orderBy('recorded_at', 'desc')->first();

            // Last 24h series (hourly points)
            $cut24 = now()->subHours(24);
            $series24h = $vitals->filter(fn($v) => $v->recorded_at >= $cut24)
                ->map(function ($v) {
                    return [
                        't' => $v->recorded_at->format('H:i'),
                        'hr' => (float) ($v->heart_rate ?? 0),
                        'sys' => (float) ($v->blood_pressure_systolic ?? 0),
                        'dia' => (float) ($v->blood_pressure_diastolic ?? 0),
                        'temp' => (float) ($v->temperature ?? 0),
                        'spo2' => (float) ($v->spo2_level ?? 0),
                        'glucose' => (float) ($v->glucose_level ?? 0),
                        'weight' => (float) ($v->weight ?? 0),
                    ];
                })->values()->all();

            // 7d daily aggregates (avg per day)
            $byDay = [];
            foreach ($vitals as $v) {
                $d = $v->recorded_at->format('Y-m-d');
                $byDay[$d]['hr'][] = (float) ($v->heart_rate ?? 0);
                $byDay[$d]['sys'][] = (float) ($v->blood_pressure_systolic ?? 0);
                $byDay[$d]['dia'][] = (float) ($v->blood_pressure_diastolic ?? 0);
                $byDay[$d]['temp'][] = (float) ($v->temperature ?? 0);
                $byDay[$d]['spo2'][] = (float) ($v->spo2_level ?? 0);
            }
            ksort($byDay);
            foreach ($byDay as $d => $vals) {
                $series7d[] = [
                    'd' => substr($d, 5),
                    'hr' => $this->avg($vals['hr'] ?? []),
                    'sys' => $this->avg($vals['sys'] ?? []),
                    'dia' => $this->avg($vals['dia'] ?? []),
                    'temp' => $this->avg($vals['temp'] ?? []),
                    'spo2' => $this->avg($vals['spo2'] ?? []),
                ];
            }

            // Summary health status based on current
            if ($current) {
                $score = 0; $checks = 0;
                // HR normal 60-100
                $checks++; $score += ($current->heart_rate && $current->heart_rate >= 60 && $current->heart_rate <= 100) ? 1 : 0;
                // BP normal ~ 90/60 to 120/80
                $checks++; $bpOk = ($current->blood_pressure_systolic && $current->blood_pressure_diastolic && $current->blood_pressure_systolic <= 120 && $current->blood_pressure_diastolic <= 80 && $current->blood_pressure_systolic >= 90 && $current->blood_pressure_diastolic >= 60);
                $score += $bpOk ? 1 : 0;
                // Temp normal 36.1-37.2 C
                $checks++; $score += ($current->temperature && $current->temperature >= 36.1 && $current->temperature <= 37.2) ? 1 : 0;
                // SpO2 normal >= 95
                $checks++; $score += ($current->spo2_level && $current->spo2_level >= 95) ? 1 : 0;
                $ratio = $checks ? ($score / $checks) : 0;
                if ($ratio >= 0.75) {
                    $summary = ['status' => 'Stable', 'color' => '#16a34a', 'note' => 'Vitals are within healthy ranges.'];
                } elseif ($ratio >= 0.5) {
                    $summary = ['status' => 'Attention', 'color' => '#f59e0b', 'note' => 'Some vitals are borderline.'];
                } else {
                    $summary = ['status' => 'Critical', 'color' => '#dc2626', 'note' => 'Multiple vitals out of range.'];
                }
            }
        }

        // Build sparkline SVG paths
        $paths = [
            'hr24' => $this->sparklinePath(array_column($series24h, 'hr')),
            'spo224' => $this->sparklinePath(array_column($series24h, 'spo2')),
            'temp24' => $this->sparklinePath(array_column($series24h, 'temp')),
            'bp24' => $this->sparklinePath(array_map(function($p){ return $p['sys'] && $p['dia'] ? ($p['sys'] + $p['dia'])/2 : 0; }, $series24h)),
        ];

        // Trends (percent change) vs previous point
        $trend = $this->computeTrends($series24h);

        // Daily prompt: after 12:00 Africa/Tunis, if no vitals recorded today
        $tz = 'Africa/Tunis';
        $nowTz = now()->copy()->setTimezone($tz);
        $todayLocal = $nowTz->toDateString();
        $hourLocal = (int) $nowTz->format('H');
        $lastRecordedOn = $current?->recorded_on ?: null; // expect migration filled this
        $showDailyPrompt = false;
        if ($hourLocal >= 12) {
            if (!$lastRecordedOn || $lastRecordedOn !== $todayLocal) {
                $showDailyPrompt = true;
            }
        }

        return view('dashboard', [
            'user' => $request->user(),
            'patient' => $patient,
            'current' => $current,
            'series24h' => $series24h,
            'series7d' => $series7d,
            'paths' => $paths,
            'trend' => $trend,
            'summary' => $summary,
            'showDailyPrompt' => $showDailyPrompt,
        ]);
    }

    public function feed(Request $request)
    {
        $user = $request->user();
        $patient = $user->patient;
        if (!$patient) return response()->json(['ok' => false]);
        $cut24 = now()->subHours(24);
        $vitals = Vital::where('patient_id', $patient->patient_id)
            ->where('recorded_at', '>=', $cut24)
            ->orderBy('recorded_at', 'asc')->get();
        $current = Vital::where('patient_id', $patient->patient_id)->orderBy('recorded_at','desc')->first();
        $series24h = $vitals->map(function ($v) {
            return [
                't' => $v->recorded_at->toIso8601String(),
                'hr' => (float) ($v->heart_rate ?? 0),
                'sys' => (float) ($v->blood_pressure_systolic ?? 0),
                'dia' => (float) ($v->blood_pressure_diastolic ?? 0),
                'temp' => (float) ($v->temperature ?? 0),
                'spo2' => (float) ($v->spo2_level ?? 0),
            ];
        })->values();
        return response()->json([
            'ok' => true,
            'current' => [
                'hr' => $current?->heart_rate,
                'sys' => $current?->blood_pressure_systolic,
                'dia' => $current?->blood_pressure_diastolic,
                'temp' => $current?->temperature,
                'spo2' => $current?->spo2_level,
            ],
            'series24h' => $series24h,
        ]);
    }

    public function exportCsv(Request $request): StreamedResponse
    {
        $user = $request->user();
        $patient = $user->patient;
        $filename = 'vitals_'.now()->format('Ymd_His').'.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];
        $callback = function() use ($patient) {
            $handle = fopen('php://output', 'w');
            fputcsv($handle, ['recorded_at','heart_rate','bp_sys','bp_dia','temperature','spo2','glucose','weight']);
            if ($patient) {
                $rows = Vital::where('patient_id', $patient->patient_id)->orderBy('recorded_at')->get();
                foreach ($rows as $r) {
                    fputcsv($handle, [
                        optional($r->recorded_at)->format('Y-m-d H:i:s'),
                        $r->heart_rate,
                        $r->blood_pressure_systolic,
                        $r->blood_pressure_diastolic,
                        $r->temperature,
                        $r->spo2_level,
                        $r->glucose_level,
                        $r->weight,
                    ]);
                }
            }
            fclose($handle);
        };
        return response()->stream($callback, 200, $headers);
    }

    private function avg(array $arr): float
    {
        $arr = array_filter($arr, fn($v) => is_numeric($v) && $v > 0);
        if (!$arr) return 0.0;
        return round(array_sum($arr)/count($arr), 1);
    }

    private function sparklinePath(array $values, int $width = 120, int $height = 36, int $pad = 2): string
    {
        $n = count($values);
        if ($n <= 1) return '';
        $min = min($values); $max = max($values);
        if ($min === $max) { $min -= 1; $max += 1; }
        $innerW = $width - 2*$pad; $innerH = $height - 2*$pad;
        $stepX = $innerW / max(1, $n-1);
        $path = '';
        for ($i=0; $i<$n; $i++) {
            $x = $pad + $i * $stepX;
            $val = $values[$i];
            $norm = ($val - $min) / ($max - $min);
            $y = $pad + $innerH - $norm * $innerH; // invert y
            $cmd = $i === 0 ? 'M' : 'L';
            $path .= sprintf('%s%.2f,%.2f ', $cmd, $x, $y);
        }
        return trim($path);
    }

    private function computeTrends(array $series24h): array
    {
        $trend = ['hr' => 0, 'bp' => 0, 'temp' => 0, 'spo2' => 0];
        $n = count($series24h);
        if ($n < 2) return $trend;
        $prev = $series24h[$n-2];
        $curr = $series24h[$n-1];
        $trend['hr'] = $this->pct($prev['hr'] ?? 0, $curr['hr'] ?? 0);
        $prevBp = ($prev['sys'] + $prev['dia'])/2; $currBp = ($curr['sys'] + $curr['dia'])/2;
        $trend['bp'] = $this->pct($prevBp, $currBp);
        $trend['temp'] = $this->pct($prev['temp'] ?? 0, $curr['temp'] ?? 0);
        $trend['spo2'] = $this->pct($prev['spo2'] ?? 0, $curr['spo2'] ?? 0);
        return $trend;
    }

    private function pct($prev, $curr): float
    {
        if (!$prev || !$curr) return 0.0;
        return round((($curr - $prev) / $prev) * 100, 1);
    }
}
