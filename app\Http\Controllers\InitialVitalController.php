<?php

namespace App\Http\Controllers;

use App\Models\Vital;
use App\Models\Patient;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class InitialVitalController extends Controller
{
    /**
     * Show welcome + initial vitals form (one-time).
     */
    public function create(Request $request)
    {
        $user = $request->user();
        if ($user->hasBaselineVitals()) {
            return redirect()->route('dashboard');
        }
        // Doctors skip vitals onboarding
        if ($user->role === 'doctor') {
            return redirect()->route('dashboard');
        }
        return view('onboarding.welcome');
    }

    /**
     * Store the one-time initial vitals.
     */
    public function store(Request $request)
    {
        $user = $request->user();
        if ($user->hasBaselineVitals() || $user->role !== 'patient') {
            return redirect()->route('dashboard');
        }

        $data = $request->validate([
            'heart_rate' => ['required','integer','min:30','max:220'],
            'blood_pressure_systolic' => ['required','integer','min:70','max:250'],
            'blood_pressure_diastolic' => ['required','integer','min:40','max:150'],
            'weight' => ['nullable','numeric','min:1','max:500'],
            'temperature' => ['nullable','numeric','min:30','max:45'],
        ]);

        $patient = $user->patient()->first();
        if (! $patient) {
            return redirect()->route('dashboard');
        }

        Vital::create([
            'patient_id' => $patient->patient_id,
            'heart_rate' => $data['heart_rate'],
            'blood_pressure_systolic' => $data['blood_pressure_systolic'],
            'blood_pressure_diastolic' => $data['blood_pressure_diastolic'],
            'weight' => $data['weight'] ?? null,
            'temperature' => $data['temperature'] ?? null,
        ]);

        return redirect()->route('dashboard')->with('welcome_complete', true);
    }
}
