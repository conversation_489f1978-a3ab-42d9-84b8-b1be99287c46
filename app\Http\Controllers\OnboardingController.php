<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\Vital;
use App\Models\User;
use App\Models\Patient;
use App\Models\Medication;

class OnboardingController extends Controller
{
    /**
     * Show the onboarding flow (single-page multi-step UI)
     */
    public function show(Request $request)
    {
        /** @var User|null $user */
        $user = Auth::user();
        $hasPrior = false;
        $patient = null;
        $latestVital = null;
        if ($user && $user->role === 'patient') {
            $patient = $user->patient()->first();
            if ($patient) {
                $hasPrior = Vital::where('patient_id', $patient->patient_id)->exists();
                $latestVital = Vital::where('patient_id', $patient->patient_id)
                    ->orderByDesc('recorded_at')
                    ->first();
            }
        }

        $isDailyFlow = (bool) $request->boolean('daily', false) || $hasPrior;
        return view('onboarding.flow', [
            'isDailyFlow' => $isDailyFlow,
            'patient' => $patient,
            'latestVital' => $latestVital,
        ]);
    }

    /**
     * Persist onboarding data in a single submission.
     * Contract: expects fields for profile, vitals, medical info, and preferences (JSON-able).
     */
    public function store(Request $request)
    {
        /** @var User|null $user */
        $user = Auth::user();
        if (!$user) {
            return redirect()->route('login');
        }

        $validated = $request->validate([
            // Vitals only (required data in this simplified onboarding)
            'height_cm' => ['nullable','numeric','min:40','max:260'],
            'weight' => ['nullable','numeric','min:2','max:400'],
            'blood_pressure_systolic' => ['nullable','integer','min:60','max:250'],
            'blood_pressure_diastolic' => ['nullable','integer','min:40','max:150'],
            'heart_rate' => ['nullable','integer','min:20','max:250'],
            'glucose_level' => ['nullable','numeric','min:20','max:600'],
            'spo2_level' => ['nullable','integer','min:50','max:100'],
            'temperature' => ['nullable','numeric','min:30','max:45'],
            // Optional insurance & history
            'insurance_provider' => ['nullable','string','max:255'],
            'policy_number' => ['nullable','string','max:100'],
            'health_history' => ['nullable','string'],
        ]);

        DB::beginTransaction();
        try {
            // Patient-only: save vitals
            if ($user->role === 'patient') {
                // Ensure a Patient profile exists for this user
                $patient = $user->patient()->first();
                if (! $patient) {
                    $patient = new Patient();
                    $patient->user_id = $user->user_id;
                    $patient->save();
                }

                if ($patient) {
                    // Optionally update patient profile with provided optional fields
                    $updated = false;
                    if (isset($validated['height_cm'])) { $patient->height_cm = $validated['height_cm']; $updated = true; }
                    if (!empty($validated['insurance_provider'])) { $patient->insurance_provider = $validated['insurance_provider']; $updated = true; }
                    if (!empty($validated['policy_number'])) { $patient->policy_number = $validated['policy_number']; $updated = true; }
                    if (!empty($validated['health_history'])) { $patient->health_history = $validated['health_history']; $updated = true; }
                    if ($updated) { $patient->save(); }

                    // Save vitals only if any provided
                    if (
                        isset($validated['weight']) ||
                        isset($validated['blood_pressure_systolic']) ||
                        isset($validated['blood_pressure_diastolic']) ||
                        isset($validated['heart_rate']) ||
                        isset($validated['glucose_level']) ||
                        isset($validated['spo2_level']) ||
                        isset($validated['temperature'])
                    ) {
                        // Compute Tunisia local date for daily uniqueness
                        $nowUtc = now();
                        $recordedOn = $nowUtc->copy()->setTimezone('Africa/Tunis')->toDateString();

                        // Upsert-or-update today's row (once per day)
                        $existing = Vital::where('patient_id', $patient->patient_id)
                            ->whereDate('recorded_on', $recordedOn)->first();
                        if ($existing) {
                            $existing->recorded_at = $nowUtc; // keep latest time
                            $existing->heart_rate = $validated['heart_rate'] ?? $existing->heart_rate;
                            $existing->blood_pressure_systolic = $validated['blood_pressure_systolic'] ?? $existing->blood_pressure_systolic;
                            $existing->blood_pressure_diastolic = $validated['blood_pressure_diastolic'] ?? $existing->blood_pressure_diastolic;
                            $existing->weight = $validated['weight'] ?? $existing->weight;
                            $existing->glucose_level = $validated['glucose_level'] ?? $existing->glucose_level;
                            $existing->spo2_level = $validated['spo2_level'] ?? $existing->spo2_level;
                            $existing->temperature = $validated['temperature'] ?? $existing->temperature;
                            $existing->save();
                        } else {
                            $vital = new Vital();
                            $vital->patient_id = $patient->patient_id;
                            $vital->recorded_at = $nowUtc;
                            $vital->recorded_on = $recordedOn;
                            $vital->heart_rate = $validated['heart_rate'] ?? null;
                            $vital->blood_pressure_systolic = $validated['blood_pressure_systolic'] ?? null;
                            $vital->blood_pressure_diastolic = $validated['blood_pressure_diastolic'] ?? null;
                            $vital->weight = $validated['weight'] ?? null;
                            $vital->glucose_level = $validated['glucose_level'] ?? null;
                            $vital->spo2_level = $validated['spo2_level'] ?? null;
                            $vital->temperature = $validated['temperature'] ?? null;
                            $vital->save();
                        }
                    }
                }
            }

            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();
            Log::error('Onboarding save failed', ['error' => $e->getMessage()]);
            return back()->withErrors(['onboarding' => 'We could not save your setup. Please try again.']);
        }

        return redirect()->route('dashboard', ['setup' => 'done']);
    }
}
