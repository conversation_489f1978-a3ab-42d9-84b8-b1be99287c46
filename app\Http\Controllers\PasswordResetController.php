<?php

namespace App\Http\Controllers;

use App\Mail\ResetPasswordOtp;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

class PasswordResetController extends Controller
{
    // Settings
    private int $codeTtlMinutes = 5; // OTP validity reduced to 5 minutes
    private int $sendThrottleSeconds = 60; // resend cooldown
    private int $maxAttempts = 5; // verify attempts window
    private int $attemptWindowSeconds = 15 * 60;
    private int $dailyQuota = 10; // max sends per email+IP per day

    public function showForgotForm()
    {
        return view('auth.forgot-password');
    }

    public function sendCode(Request $request)
    {
        $request->validate([
            'email' => ['required','email']
        ]);
        $email = strtolower(trim($request->input('email')));

        // Ensure an account exists for this email before proceeding
        $existing = User::where('email', $email)->first();
        if (!$existing) {
            // Audit (best-effort) for missing account
            try {
                if (Schema::hasTable('password_reset_attempts')) {
                    DB::table('password_reset_attempts')->insert([
                        'email' => $email,
                        'ip' => $request->ip(),
                        'action' => 'fail',
                        'meta' => json_encode(['reason' => 'no_account']),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            } catch (\Throwable $e) { /* ignore logging errors */ }
            return back()->withErrors(['email' => 'No account found with this email address.'])->withInput();
        }

        // Daily quota and throttle to prevent abuse
        $throttleKey = 'pwd:send:' . sha1($email . '|' . $request->ip());
        $quotaKey = 'pwd:quota:' . sha1($email . '|' . $request->ip());
        $sentCount = (int) Cache::get($quotaKey, 0);
        if ($sentCount >= $this->dailyQuota) {
            return back()->with('status', 'If that email exists, a code was just sent. Please check your inbox.');
        }
        if (Cache::has($throttleKey)) {
            return back()->with('status', 'If that email exists, a code was just sent. Please check your inbox.');
        }

        // Generate 6-digit code and hash it
        $code = random_int(100000, 999999);
        $hashed = Hash::make((string) $code);
        $now = now();

        // Upsert into password_reset_tokens
        DB::table('password_reset_tokens')->updateOrInsert(
            ['email' => $email],
            [
                'token' => $hashed,
                'created_at' => $now,
            ]
        );

        // Send email (generic response to avoid user enumeration)
        try {
            Mail::to($email)->send(new ResetPasswordOtp($code, $this->codeTtlMinutes));
        } catch (\Throwable $e) {
            // Do not leak errors; still pretend it was sent
        }

        // Audit log (best-effort)
        try {
            if (Schema::hasTable('password_reset_attempts')) {
                DB::table('password_reset_attempts')->insert([
                    'email' => $email,
                    'ip' => $request->ip(),
                    'action' => 'send',
                    'meta' => json_encode(['throttle' => $this->sendThrottleSeconds, 'ttl' => $this->codeTtlMinutes]),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        } catch (\Throwable $e) { /* ignore logging errors */ }

        // Increment quota and set throttle
        $ttlToMidnight = now()->endOfDay()->diffInSeconds(now());
        Cache::put($quotaKey, $sentCount + 1, $ttlToMidnight);
        Cache::put($throttleKey, true, $this->sendThrottleSeconds);

        // Redirect to verify step with email prefilled
        return redirect()->route('password.verify', ['email' => $email, 'from' => 'forgot'])
            ->with('status', 'If that email exists, we emailed a verification code.');
    }

    public function showResetForm(Request $request)
    {
        // Optional prefill via query string
        return view('auth.reset-password', [
            'email' => $request->query('email', ''),
        ]);
    }

    public function reset(Request $request)
    {
        $data = $request->validate([
            'email' => ['required','email'],
            'code' => ['required','digits:6'],
            'password' => ['required','string','min:8','confirmed'],
        ]);
        $email = strtolower(trim($data['email']));
        $code = (string) $data['code'];

        $attemptKey = 'pwd:attempts:' . sha1($email . '|' . $request->ip());
        $attempts = (int) Cache::get($attemptKey, 0);
        if ($attempts >= $this->maxAttempts) {
            return back()->withErrors(['code' => 'Too many attempts. Please request a new code later.'])->withInput();
        }

    $row = DB::table('password_reset_tokens')->where('email', $email)->first();
        if (!$row) {
            try {
                if (Schema::hasTable('password_reset_attempts')) {
                    DB::table('password_reset_attempts')->insert([
                        'email' => $email,
                        'ip' => $request->ip(),
                        'action' => 'fail',
                        'meta' => json_encode(['reason' => 'no_row']),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            } catch (\Throwable $e) { /* ignore logging errors */ }
            Cache::put($attemptKey, $attempts + 1, $this->attemptWindowSeconds);
            return back()->withErrors(['code' => 'Invalid or expired code.'])->withInput();
        }
    $createdAt = $row->created_at ? Carbon::parse($row->created_at) : now()->subYears(10);
    if ($createdAt->lt(now()->subMinutes($this->codeTtlMinutes))) {
            try {
                if (Schema::hasTable('password_reset_attempts')) {
                    DB::table('password_reset_attempts')->insert([
                        'email' => $email,
                        'ip' => $request->ip(),
                        'action' => 'fail',
                        'meta' => json_encode(['reason' => 'expired']),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            } catch (\Throwable $e) { /* ignore logging errors */ }
            Cache::put($attemptKey, $attempts + 1, $this->attemptWindowSeconds);
            return back()->withErrors(['code' => 'Invalid or expired code.'])->withInput();
        }

        // Verify hashed token
        if (!Hash::check($code, $row->token)) {
            try {
                if (Schema::hasTable('password_reset_attempts')) {
                    DB::table('password_reset_attempts')->insert([
                        'email' => $email,
                        'ip' => $request->ip(),
                        'action' => 'fail',
                        'meta' => json_encode(['reason' => 'mismatch']),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            } catch (\Throwable $e) { /* ignore logging errors */ }
            Cache::put($attemptKey, $attempts + 1, $this->attemptWindowSeconds);
            return back()->withErrors(['code' => 'Invalid or expired code.'])->withInput();
        }

        // Update user password_hash
        /** @var User|null $user */
        $user = User::where('email', $email)->first();
        if ($user) {
            $user->password_hash = Hash::make($data['password']);
            $user->save();
        }

        // Delete the token
        DB::table('password_reset_tokens')->where('email', $email)->delete();
        try {
            if (Schema::hasTable('password_reset_attempts')) {
                DB::table('password_reset_attempts')->insert([
                    'email' => $email,
                    'ip' => $request->ip(),
                    'action' => 'success',
                    'meta' => json_encode(['reset' => true]),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        } catch (\Throwable $e) { /* ignore logging errors */ }
        Cache::forget($attemptKey);

        return redirect()->route('login')->with('status', 'Password has been reset. Please sign in.');
    }

    // New two-step: 1) Verify code page
    public function showVerifyForm(Request $request)
    {
        return view('auth.reset-verify', [
            'email' => $request->query('email', ''),
            'from' => $request->query('from'),
            'ttl' => $this->codeTtlMinutes,
        ]);
    }

    public function verify(Request $request)
    {
        $data = $request->validate([
            'email' => ['required','email'],
            'code' => ['required','digits:6'],
        ]);
        $email = strtolower(trim($data['email']));
        $code = (string) $data['code'];

        $attemptKey = 'pwd:attempts:' . sha1($email . '|' . $request->ip());
        $attempts = (int) Cache::get($attemptKey, 0);
        if ($attempts >= $this->maxAttempts) {
            return back()->withErrors(['code' => 'Too many attempts. Please request a new code later.'])->withInput();
        }
        $row = DB::table('password_reset_tokens')->where('email', $email)->first();
        if (!$row) {
            try { if (Schema::hasTable('password_reset_attempts')) DB::table('password_reset_attempts')->insert(['email'=>$email,'ip'=>$request->ip(),'action'=>'fail','meta'=>json_encode(['reason'=>'no_row']),'created_at'=>now(),'updated_at'=>now()]); } catch(\Throwable $e){}
            Cache::put($attemptKey, $attempts + 1, $this->attemptWindowSeconds);
            return back()->withErrors(['code' => 'Invalid or expired code.'])->withInput();
        }
    $createdAt = $row->created_at ? Carbon::parse($row->created_at) : now()->subYears(10);
    if ($createdAt->lt(now()->subMinutes($this->codeTtlMinutes))) {
            try { if (Schema::hasTable('password_reset_attempts')) DB::table('password_reset_attempts')->insert(['email'=>$email,'ip'=>$request->ip(),'action'=>'fail','meta'=>json_encode(['reason'=>'expired']),'created_at'=>now(),'updated_at'=>now()]); } catch(\Throwable $e){}
            Cache::put($attemptKey, $attempts + 1, $this->attemptWindowSeconds);
            return back()->withErrors(['code' => 'Invalid or expired code.'])->withInput();
        }
        if (!Hash::check($code, $row->token)) {
            try { if (Schema::hasTable('password_reset_attempts')) DB::table('password_reset_attempts')->insert(['email'=>$email,'ip'=>$request->ip(),'action'=>'fail','meta'=>json_encode(['reason'=>'mismatch']),'created_at'=>now(),'updated_at'=>now()]); } catch(\Throwable $e){}
            Cache::put($attemptKey, $attempts + 1, $this->attemptWindowSeconds);
            return back()->withErrors(['code' => 'Invalid or expired code.'])->withInput();
        }
        // Mark verified in session, clear attempt counter, and go to new password page
        session(['pwd_verified_email' => $email]);
        Cache::forget($attemptKey);
        return redirect()->route('password.new', ['email' => $email]);
    }

    // New two-step: 2) Set new password
    public function showNewPasswordForm(Request $request)
    {
        $email = $request->query('email','');
        if (session('pwd_verified_email') !== $email) {
            return redirect()->route('password.verify', ['email' => $email]);
        }
        return view('auth.reset-new-password', [ 'email' => $email ]);
    }

    public function setNewPassword(Request $request)
    {
        $data = $request->validate([
            'email' => ['required','email'],
            'password' => ['required','string','min:8','confirmed'],
        ]);
        $email = strtolower(trim($data['email']));
        if (session('pwd_verified_email') !== $email) {
            return redirect()->route('password.verify', ['email' => $email]);
        }
        // Update and cleanup
        $user = User::where('email', $email)->first();
        if ($user) {
            $user->password_hash = Hash::make($data['password']);
            $user->save();
        }
        DB::table('password_reset_tokens')->where('email', $email)->delete();
        try { if (Schema::hasTable('password_reset_attempts')) DB::table('password_reset_attempts')->insert(['email'=>$email,'ip'=>$request->ip(),'action'=>'success','meta'=>json_encode(['reset'=>true]),'created_at'=>now(),'updated_at'=>now()]); } catch(\Throwable $e){}
        session()->forget('pwd_verified_email');
        return redirect()->route('password.success')->with('status', 'Password has been reset.');
    }

    public function success(Request $request)
    {
        // Render a small success animation page and auto-redirect to login
        return view('auth.reset-success', [
            'next' => route('login'),
            'message' => session('status', 'Your password was changed successfully.'),
        ]);
    }
}
