<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use App\Models\Patient;
use App\Models\Vital;

class ProfileController extends Controller
{
    public function show(Request $request)
    {
        $user = $request->user();
        $patient = $user->patient; // may be null for doctors/admin
        $latestVital = $patient ? $patient->vitals()->latest('recorded_at')->first() : null;

        // Time filter for vitals history
        $range = $request->query('range', '30d'); // 7d|30d|90d|365d|all|custom
        $from = $request->query('from'); // YYYY-MM-DD
        $to = $request->query('to');     // YYYY-MM-DD

        $vitals = null;
        if ($patient) {
            $q = Vital::where('patient_id', $patient->patient_id);
            // Use recorded_on (Africa/Tunis local DATE) for date range filters
            if ($range === 'custom') {
                if ($from && $to) {
                    $q->whereBetween('recorded_on', [$from, $to]);
                } elseif ($from) {
                    $q->where('recorded_on', '>=', $from);
                } elseif ($to) {
                    $q->where('recorded_on', '<=', $to);
                }
            } elseif ($range !== 'all') {
                $daysMap = [
                    '7d' => 7,
                    '30d' => 30,
                    '90d' => 90,
                    '365d' => 365,
                ];
                $days = $daysMap[$range] ?? 30;
                $since = now()->setTimezone('Africa/Tunis')->subDays($days)->toDateString();
                $q->where('recorded_on', '>=', $since);
            }
            $vitals = $q->orderByDesc('recorded_at')->paginate(15)->withQueryString();
        }

        return view('profile.index', compact('user','patient','latestVital','vitals','range','from','to'));
    }

    public function updateAccount(Request $request)
    {
        $user = $request->user();
        $data = $request->validate([
            'name' => ['required','string','max:255'],
        ]);
        $user->name = $data['name'];
        $user->save();
        return back()->with('status', 'Account updated successfully.');
    }

    public function updatePatient(Request $request)
    {
        $user = $request->user();
        if ($user->role !== 'patient') {
            return back();
        }
        $data = $request->validate([
            'date_of_birth' => ['nullable','date'],
            'gender' => ['nullable', Rule::in(['male','female','other'])],
            'address' => ['nullable','string','max:500'],
            'phone_number' => ['nullable','string','max:50'],
            'emergency_contact' => ['nullable','string','max:255'],
            'insurance_provider' => ['nullable','string','max:255'],
            'policy_number' => ['nullable','string','max:255'],
            'health_history' => ['nullable','string','max:2000'],
            'height_cm' => ['nullable','numeric','min:40','max:260'],
            'allergies' => ['nullable','string','max:500'],
            'medical_conditions' => ['nullable','string','max:500'],
        ]);

        $patient = $user->patient ?: new Patient(['user_id' => $user->user_id]);
        $patient->fill($data);
        $patient->user_id = $user->user_id;
        $patient->save();

        return back()->with('status', 'Patient details updated successfully.');
    }

    public function updatePhoto(Request $request)
    {
        $user = $request->user();
        $request->validate([
            'photo' => ['required','image','mimes:jpeg,png,jpg,gif,webp','max:4096'],
        ]);

        $path = $request->file('photo')->store('avatars', 'public');
        $publicUrl = asset('storage/'.$path);
        $user->photo_url = $publicUrl;
        $user->save();

        return back()->with('status', 'Photo uploaded successfully.');
    }
}
