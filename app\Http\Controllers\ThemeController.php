<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;

class ThemeController extends Controller
{
    /**
     * Toggle the theme cookie between dark and light.
     */
    public function toggle(Request $request)
    {
        $current = $request->cookie('theme'); // 'dark' | 'light' | null
        $next = $current === 'dark' ? 'light' : 'dark';

        $minutes = 60 * 24 * 365; // 1 year
        $cookie = Cookie::make('theme', $next, $minutes, '/', null, false, false, false, 'Lax');

        if ($request->wantsJson()) {
            return response()->json(['ok' => true, 'theme' => $next])->with<PERSON><PERSON>ie($cookie);
        }

        return back()->with<PERSON><PERSON><PERSON>($cookie);
    }
}
