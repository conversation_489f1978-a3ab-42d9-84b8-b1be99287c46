<?php

namespace App\Http\Middleware;

use App\Models\User;
use App\Services\JwtService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AuthenticateJwt
{
    public function handle(Request $request, Closure $next): Response
    {
        $auth = $request->header('Authorization', '');
        if (!str_starts_with($auth, 'Bearer ')) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }
        $token = substr($auth, 7);
        $claims = JwtService::verify($token);
        if (!$claims || empty($claims['sub'])) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }
        // denylist check using cache
        if (function_exists('cache')) {
            $jti = $claims['jti'] ?? null;
            if ($jti && cache()->has('jwt:blacklist:' . $jti)) {
                return response()->json(['message' => 'Unauthorized'], 401);
            }
        }
        $user = User::where('user_id', $claims['sub'])->first();
        if (!$user) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }
        // Attach user for downstream usage
        $request->setUserResolver(fn() => $user);
        // Attach claims for downstream (refresh, logout)
        $request->attributes->set('jwt_claims', $claims);
        $request->attributes->set('jwt_token', $token);
        return $next($request);
    }
}
