<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class BasicHeaders
{
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // Ensure UTF-8 charset on HTML responses
        $contentType = $response->headers->get('Content-Type');
        if (!$contentType) {
            // Heuristic: if response body looks like HTML
            $body = method_exists($response, 'getContent') ? (string) $response->getContent() : '';
            if (stripos($body, '<!DOCTYPE html') !== false || stripos($body, '<html') !== false) {
                $response->headers->set('Content-Type', 'text/html; charset=utf-8');
            }
        } elseif (str_starts_with(strtolower($contentType), 'text/html') && stripos($contentType, 'charset=') === false) {
            $response->headers->set('Content-Type', 'text/html; charset=utf-8');
        }

        // Add nosniff
        if (!$response->headers->has('X-Content-Type-Options')) {
            $response->headers->set('X-Content-Type-Options', 'nosniff');
        }

        // Default cache control for dynamic HTML (avoid caching)
        $path = $request->path();
        $isAsset = preg_match('#\.(css|js|png|jpg|jpeg|gif|svg|webp|ico|woff2?|ttf|otf)$#i', $path) === 1 ||
                   str_starts_with($path, 'build/') || str_starts_with($path, 'public/build/');
        if (!$isAsset && !$response->headers->has('Cache-Control')) {
            $response->headers->set('Cache-Control', 'no-store');
        }

        return $response;
    }
}
