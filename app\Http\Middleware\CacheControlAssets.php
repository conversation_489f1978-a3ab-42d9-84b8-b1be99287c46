<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CacheControlAssets
{
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // Only set cache headers in production for static versioned assets
        if (app()->environment('production')) {
            $path = $request->path();
            $isAsset = preg_match('#^(build/|public/build/|storage/|assets/|.*\.(css|js|png|jpg|jpeg|gif|svg|webp|ico|woff2?|ttf|otf))$#i', $path);
            if ($isAsset) {
                // One year + immutable, rely on hashed filenames from Vite
                $response->headers->set('Cache-Control', 'public, max-age=31536000, immutable');
            }
        }

        return $response;
    }
}
