<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RedirectIfOnboardingIncomplete
{
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        if (! $user) {
            return $next($request);
        }

        // <PERSON><PERSON> and doctors bypass onboarding; Patients must have at least one vital
        if (in_array($user->role, ['admin'])) {
            return $next($request);
        }
        if ($user->role === 'doctor') {
            return $next($request);
        }
        if ($user->role === 'patient' && ! $user->hasBaselineVitals()) {
            // Allow a one-time bypass in this session (for "skip for now")
            if (session('onboarding_skip_once')) {
                session()->forget('onboarding_skip_once');
                return $next($request);
            }
            return redirect()->route('onboarding.show');
        }

        return $next($request);
    }
}
