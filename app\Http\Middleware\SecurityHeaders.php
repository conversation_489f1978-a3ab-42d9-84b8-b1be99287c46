<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class SecurityHeaders
{
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);
        // Modern headers
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        // Avoid X-Frame-Options; use CSP frame-ancestors instead
        $csp = "default-src 'self' 'unsafe-inline' 'unsafe-eval' http://[::1]:5173 http://127.0.0.1:5173; " .
               "img-src 'self' data: https:; style-src 'self' 'unsafe-inline' http://[::1]:5173 http://127.0.0.1:5173; " .
               "script-src 'self' 'unsafe-inline' 'unsafe-eval' http://[::1]:5173 http://127.0.0.1:5173; " .
               "connect-src 'self' http://[::1]:5173 http://127.0.0.1:5173; frame-ancestors 'self';";
        $response->headers->set('Content-Security-Policy', $csp);
        return $response;
    }
}
