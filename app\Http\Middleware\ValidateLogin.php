<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ValidateLogin
{
    public function handle(Request $request, Closure $next)
    {
        if ($request->isMethod('post')) {
            $validator = Validator::make($request->all(), [
                'email' => ['required','email'],
                'password' => ['required','string','min:6'],
            ], [
                'email.required' => 'Please enter your email.',
                'email.email' => 'Please enter a valid email address.',
                'password.required' => 'Please enter your password.',
            ]);
            if ($validator->fails()) {
                return redirect()->back()->withErrors($validator)->withInput();
            }
        }
        return $next($request);
    }
}
