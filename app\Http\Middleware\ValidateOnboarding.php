<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class ValidateOnboarding
{
    public function handle(Request $request, Closure $next)
    {
        // Only validate on POST submissions
        if ($request->isMethod('post')) {
            $request->validate([
                // Step 3: Profile
                'name' => ['required','string','max:255'],
                'email' => ['required','email'],
                'date_of_birth' => ['nullable','date'],
                'gender' => ['nullable','in:male,female,other'],
                // Step 4: Vitals
                'height_cm' => ['nullable','numeric','min:40','max:260'],
                'weight' => ['nullable','numeric','min:2','max:400'],
                'blood_pressure_systolic' => ['nullable','integer','min:60','max:250'],
                'blood_pressure_diastolic' => ['nullable','integer','min:40','max:150'],
                'heart_rate' => ['nullable','integer','min:20','max:250'],
                // Step 5: Medical info
                'allergies' => ['nullable','string'],
                'medical_conditions' => ['nullable','string'],
                'medications' => ['nullable','array'],
                'medications.*.name' => ['required_with:medications','string','max:255'],
                'medications.*.dosage' => ['nullable','string','max:100'],
                'medications.*.frequency' => ['nullable','string','max:100'],
                // Step 6: Preferences
                'preferences' => ['nullable','array'],
            ], [
                'name.required' => 'Please enter your name.',
                'email.required' => 'Please enter your email.',
                'email.email' => 'Please enter a valid email address.',
                'gender.in' => 'Gender must be Male, Female, or Other.',
                'height_cm.numeric' => 'Height must be a number in cm.',
                'weight.numeric' => 'Weight must be a number in kg.',
                'blood_pressure_systolic.integer' => 'Systolic BP must be an integer.',
                'blood_pressure_diastolic.integer' => 'Diastolic BP must be an integer.',
                'heart_rate.integer' => 'Heart rate must be an integer (bpm).',
                'medications.array' => 'Medications must be a list.',
                'medications.*.name.required_with' => 'Medication name is required when adding medications.',
            ]);
        }

        return $next($request);
    }
}
