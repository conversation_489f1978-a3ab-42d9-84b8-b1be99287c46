<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;

class ValidateRegister
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        if ($request->isMethod('post')) {
            $rules = [
                'name' => ['required','string','max:255'],
                'email' => ['required','string','email','max:255','unique:users,email'],
                'password' => ['required','confirmed', Password::defaults()],
                'role' => ['required','in:patient,doctor'],
                // Phone: accept any separators/formatting as long as there are exactly 8 digits total
                'phone' => ['nullable', function ($attribute, $value, $fail) {
                    $digitsOnly = preg_replace('/\D+/', '', (string) $value);
                    if (strlen($digitsOnly) !== 8) {
                        $fail('Phone must contain exactly 8 digits.');
                    }
                }],
                'terms' => ['accepted'],
                // Optional extras
                'insurance_provider' => ['nullable','string','max:255'],
                'policy_number' => ['nullable','string','max:100'],
                'health_history' => ['nullable','string'],
                'specialty' => ['nullable','string','max:255'],
                'license_number' => ['nullable','string','max:100'],
                'hospital' => ['nullable','string','max:255'],
            ];

            $messages = [
                'name.required' => 'Please enter your name.',
                'email.required' => 'Please enter your email.',
                'email.email' => 'Please enter a valid email address.',
                'password.required' => 'Please enter a password.',
                'password.confirmed' => 'Password confirmation does not match.',
                'role.required' => 'Please select a role.',
                'role.in' => 'Role must be patient or doctor.',
                // Custom phone message is handled in the closure above
                'terms.accepted' => 'You must agree to the Terms & Privacy to continue.',
            ];

            $validator = Validator::make($request->all(), $rules, $messages);
            if ($validator->fails()) {
                return redirect()->back()->withErrors($validator)->withInput();
            }
        }

        return $next($request);
    }
}
