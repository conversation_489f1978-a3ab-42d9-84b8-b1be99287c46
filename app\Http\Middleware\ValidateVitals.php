<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ValidateVitals
{
    public function handle(Request $request, Closure $next)
    {
        if ($request->isMethod('post')) {
            $validator = Validator::make($request->all(), [
                'height_cm' => ['nullable','numeric','min:40','max:260'],
                'weight' => ['nullable','numeric','min:2','max:400'],
                'blood_pressure_systolic' => ['nullable','integer','min:60','max:250'],
                'blood_pressure_diastolic' => ['nullable','integer','min:40','max:150'],
                'heart_rate' => ['nullable','integer','min:20','max:250'],
                'glucose_level' => ['nullable','numeric','min:20','max:600'],
                'spo2_level' => ['nullable','integer','min:50','max:100'],
                'temperature' => ['nullable','numeric','min:30','max:45'],
            ], [
                'height_cm.numeric' => 'Height must be a number in cm (40–260).',
                'weight.numeric' => 'Weight must be a number in kg (2–400).',
                'blood_pressure_systolic.integer' => 'Systolic BP must be an integer (60–250).',
                'blood_pressure_diastolic.integer' => 'Diastolic BP must be an integer (40–150).',
                'heart_rate.integer' => 'Heart rate must be an integer (20–250 bpm).',
                'glucose_level.numeric' => 'Glucose must be a number (20–600 mg/dL).',
                'spo2_level.integer' => 'SpO₂ must be an integer percentage (50–100%).',
                'temperature.numeric' => 'Temperature must be a number in °C (30–45).',
            ]);

            $validator->after(function($v) use ($request) {
                $sys = $request->input('blood_pressure_systolic');
                $dia = $request->input('blood_pressure_diastolic');
                if ($sys !== null && $dia !== null && is_numeric($sys) && is_numeric($dia)) {
                    if ((int)$sys <= (int)$dia) {
                        $v->errors()->add('blood_pressure_systolic', 'Systolic pressure must be greater than diastolic.');
                    }
                }
            });

            $validator->validate();
        }

        return $next($request);
    }
}
