<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

use Illuminate\Contracts\Queue\ShouldQueue;

class ResetPasswordOtp extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public function __construct(public int $code, public int $ttlMinutes)
    {
    }

    public function build()
    {
        return $this->subject('Your password reset code')
            ->view('emails.reset-password-otp')
            ->with([
                'code' => $this->code,
                'ttl' => $this->ttlMinutes,
            ]);
    }
}
