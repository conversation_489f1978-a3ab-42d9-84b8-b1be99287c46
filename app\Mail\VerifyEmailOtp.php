<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class VerifyEmailOtp extends Mailable
{
    use Queueable, SerializesModels;

    public string $name;
    public string $code;
    public int $expiresInMinutes;

    public function __construct(string $name, string $code, int $expiresInMinutes)
    {
        $this->name = $name;
        $this->code = $code;
        $this->expiresInMinutes = $expiresInMinutes;
    }

    public function build()
    {
        return $this->subject('Your Verification Code')
            ->markdown('emails.verify-otp')
            ->with([
                'name' => $this->name,
                'code' => $this->code,
                'expiresInMinutes' => $this->expiresInMinutes,
            ]);
    }
}
