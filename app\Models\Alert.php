<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Alert extends Model
{
    use HasFactory;

    protected $primaryKey = 'alert_id';
    public $incrementing = true;
    protected $keyType = 'int';
    public $timestamps = false; // has created_at only

    protected $fillable = [
        'patient_id',
        'type',
        'message',
        'severity',
        'created_at',
        'is_resolved',
    ];

    protected function casts(): array
    {
        return [
            'is_resolved' => 'boolean',
            'created_at' => 'datetime',
        ];
    }

    public function patient()
    {
        return $this->belongsTo(Patient::class, 'patient_id', 'patient_id');
    }
}
