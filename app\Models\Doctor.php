<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Doctor extends Model
{
    use HasFactory;

    protected $primaryKey = 'doctor_id';
    public $incrementing = true;
    protected $keyType = 'int';
    public $timestamps = false; // table does not have created_at/updated_at

    protected $fillable = [
        'user_id',
        'specialty',
        'license_number',
        'hospital_affiliation',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }
}
