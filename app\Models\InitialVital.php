<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InitialVital extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'heart_rate',
        'systolic',
        'diastolic',
        'weight',
        'height',
        'bmi',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
