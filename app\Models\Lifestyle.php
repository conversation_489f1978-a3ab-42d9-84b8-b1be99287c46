<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Lifestyle extends Model
{
    use HasFactory;

    protected $primaryKey = 'lifestyle_id';
    public $incrementing = true;
    protected $keyType = 'int';

    protected $fillable = [
        'patient_id','smoking_status','alcohol_consumption','activity_level','sleep_hours','diet_notes',
    ];
}
