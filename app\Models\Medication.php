<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Medication extends Model
{
    use HasFactory;

    protected $primaryKey = 'medication_id';
    public $incrementing = true;
    protected $keyType = 'int';
    public $timestamps = false; // keep as false; no timestamps columns defined

    protected $fillable = [
        'patient_id','appointment_id',
        'name',
        'dosage',
        'frequency',
        'start_date',
        'end_date',
        'reminder_time',
    ];

    public function patient()
    {
        return $this->belongsTo(Patient::class, 'patient_id', 'patient_id');
    }

    public function appointment()
    {
        return $this->belongsTo(Appointment::class, 'appointment_id', 'appointment_id');
    }
}
