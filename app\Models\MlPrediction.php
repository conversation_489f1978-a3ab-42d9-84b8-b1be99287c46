<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class MlPrediction extends Model
{
    use HasFactory;

    protected $primaryKey = 'prediction_id';
    public $incrementing = true;
    protected $keyType = 'int';
    public $timestamps = false; // has predicted_at only

    protected $fillable = [
        'patient_id',
        'model_type',
        'result',
        'confidence_score',
        'predicted_at',
    ];

    protected function casts(): array
    {
        return [
            'predicted_at' => 'datetime',
        ];
    }

    public function patient()
    {
        return $this->belongsTo(Patient::class, 'patient_id', 'patient_id');
    }
}
