<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\Vital;

class Patient extends Model
{
    use HasFactory;

    protected $primaryKey = 'patient_id';
    public $incrementing = true;
    protected $keyType = 'int';
    public $timestamps = false; // table does not have created_at/updated_at

    protected $fillable = [
        'user_id',
        'date_of_birth',
        'gender',
        'address',
        'phone_number',
        'emergency_contact',
        'insurance_provider',
        'policy_number',
        'health_history',
        // Additional fields present in migrations and used in onboarding
        'height_cm',
        'allergies',
        'medical_conditions',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }

    public function vitals()
    {
        return $this->hasMany(Vital::class, 'patient_id', 'patient_id');
    }

    public function medications()
    {
        return $this->hasMany(Medication::class, 'patient_id', 'patient_id');
    }
}
