<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Report extends Model
{
    use HasFactory;

    protected $primaryKey = 'report_id';
    public $incrementing = true;
    protected $keyType = 'int';
    public $timestamps = false;

    protected $fillable = [
        'vital_id','generated_at','type','summary','recommendations',
    ];

    public function vital()
    {
        return $this->belongsTo(Vital::class, 'vital_id', 'vital_id');
    }
}
