<?php

namespace App\Models;

// Email verification contract
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasFactory, Notifiable;

    /**
     * Custom primary key mapping to match existing schema (user_id)
     */
    protected $primaryKey = 'user_id';
    public $incrementing = true;
    protected $keyType = 'int';

    /**
     * Mass assignable attributes (use password_hash not password)
     */
    protected $fillable = [
        'name',
        'email',
        'password_hash',
        'role',
        'photo_url',
    ];

    /**
     * Hidden attributes
     */
    protected $hidden = [
        'password_hash',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'verification_code_expires_at' => 'datetime',
            'last_verification_sent_at' => 'datetime',
        ];
    }

    /**
     * Return password hash for auth system compatibility.
     */
    public function getAuthPassword()
    {
        return $this->password_hash;
    }

    /**
     * Patient profile (if role == patient)
     */
    public function patient()
    {
        return $this->hasOne(Patient::class, 'user_id', 'user_id');
    }

    /** Doctor profile (if role == doctor) */
    public function doctor()
    {
        return $this->hasOne(Doctor::class, 'user_id', 'user_id');
    }

    /** Vitals via patient profile */
    public function vitals()
    {
        return $this->patient()?->first()?->vitals();
    }

    /** Check whether baseline vitals exist (first row) */
    public function hasBaselineVitals(): bool
    {
        if ($this->role !== 'patient') {
            return true; // doctors skip vitals onboarding
        }
        $patient = $this->patient()->first();
        return $patient ? $patient->vitals()->exists() : false;
    }
}
