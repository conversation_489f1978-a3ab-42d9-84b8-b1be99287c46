<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Vital extends Model
{
    use HasFactory;

    protected $primaryKey = 'vital_id';
    public $incrementing = true;
    protected $keyType = 'int';
    public $timestamps = false; // has recorded_at only

    protected $fillable = [
        'patient_id',
        'recorded_at',
        'heart_rate',
        'blood_pressure_systolic',
        'blood_pressure_diastolic',
        'glucose_level',
        'spo2_level',
        'weight',
        'temperature',
    ];

    protected function casts(): array
    {
        return [
            'recorded_at' => 'datetime',
        ];
    }

    public function patient()
    {
        return $this->belongsTo(Patient::class, 'patient_id', 'patient_id');
    }
}
