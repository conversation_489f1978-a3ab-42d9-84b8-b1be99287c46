<?php

namespace App\Services;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

class JwtService
{
    public static function issue(array $claims): string
    {
        $now = time();
        $ttl = (int) Config::get('jwt.ttl', 60);
        $payload = array_merge([
            'iss' => Config::get('jwt.issuer'),
            'aud' => Config::get('jwt.audience'),
            'iat' => $now,
            'nbf' => $now,
            'exp' => $now + ($ttl * 60),
            // unique token id for blacklist/invalidation
            'jti' => bin2hex(random_bytes(16)),
        ], $claims);
        return JWT::encode($payload, Config::get('jwt.secret'), 'HS256');
    }

    public static function verify(string $token): ?array
    {
        try {
            $decoded = JWT::decode($token, new Key(Config::get('jwt.secret'), 'HS256'));
            return (array) $decoded;
        } catch (\Throwable $e) {
            Log::warning('JWT verify failed', ['error' => $e->getMessage()]);
            return null;
        }
    }
}
