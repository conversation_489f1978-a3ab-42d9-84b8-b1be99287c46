<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->append(\App\Http\Middleware\BasicHeaders::class);

        // Route middleware aliases
        $middleware->alias([
            'validate.login' => \App\Http\Middleware\ValidateLogin::class,
            'validate.register' => \App\Http\Middleware\ValidateRegister::class,
            'onboarding.required' => \App\Http\Middleware\RedirectIfOnboardingIncomplete::class,
            'validate.vitals' => \App\Http\Middleware\ValidateVitals::class,
            'jwt' => \App\Http\Middleware\AuthenticateJwt::class,
            'admin' => \App\Http\Middleware\EnsureAdmin::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
