<?php

return [
    // IMPORTANT: set JWT_SECRET in .env; fallback derives a stable key from APP_KEY
    'secret' => env('JWT_SECRET', substr(hash('sha256', (string) env('APP_KEY', 'fallback-app-key')), 0, 64)),
    'issuer' => env('JWT_ISSUER', env('APP_URL', 'http://localhost')),
    'audience' => env('JWT_AUDIENCE', env('APP_URL', 'http://localhost')),
    'ttl' => env('JWT_TTL', 60), // minutes
    'refresh_ttl' => env('JWT_REFRESH_TTL', 20160), // minutes (14 days)
];
