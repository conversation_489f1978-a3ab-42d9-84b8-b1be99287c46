<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // We now rely on a pre-existing custom `users` table (with primary key `user_id` and `password_hash`).
        // Skip creating the default Laravel users table if it already exists to avoid conflicts with the manual schema.
        if (!Schema::hasTable('users')) {
            Schema::create('users', function (Blueprint $table) {
                $table->increments('user_id'); // INT AUTO_INCREMENT
                $table->string('name');
                $table->string('email')->unique();
                // Leave email_verified_at & remember_token additions to the later alter migration we introduced.
                $table->string('password_hash');
                $table->enum('role', ['patient','doctor','admin'])->default('patient');
                $table->timestamps();
            });
        }

        if (!Schema::hasTable('password_reset_tokens')) {
            Schema::create('password_reset_tokens', function (Blueprint $table) {
                $table->string('email')->primary();
                $table->string('token');
                $table->timestamp('created_at')->nullable();
            });
        }

        if (!Schema::hasTable('sessions')) {
            Schema::create('sessions', function (Blueprint $table) {
                $table->string('id')->primary();
                // Use unsignedInteger without FK constraint (custom PK name differs)
                $table->unsignedInteger('user_id')->nullable()->index();
                $table->string('ip_address', 45)->nullable();
                $table->text('user_agent')->nullable();
                $table->longText('payload');
                $table->integer('last_activity')->index();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Intentionally do NOT drop users table automatically because it may be a pre-existing external schema.
        // If you really want to drop it, do so manually.
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
