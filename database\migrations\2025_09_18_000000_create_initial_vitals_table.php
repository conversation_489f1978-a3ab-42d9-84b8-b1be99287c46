<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        // Deprecated migration: initial baseline vitals are stored in `vitals` now.
        // Intentionally no-op to avoid conflicting FK against custom users.user_id.
        if (Schema::hasTable('initial_vitals')) {
            return;
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('initial_vitals');
    }
};
