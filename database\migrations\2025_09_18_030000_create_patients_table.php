<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (!Schema::hasTable('patients')) {
            Schema::create('patients', function (Blueprint $table) {
                $table->increments('patient_id');
                $table->unsignedInteger('user_id');
                $table->date('date_of_birth')->nullable();
                $table->string('gender', 10)->nullable();
                $table->string('address', 255)->nullable();
                $table->string('phone_number', 20)->nullable();
                $table->string('emergency_contact', 255)->nullable();

                $table->foreign('user_id')->references('user_id')->on('users')->onDelete('cascade');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('patients');
    }
};
