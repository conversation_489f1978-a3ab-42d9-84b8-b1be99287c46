<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (!Schema::hasTable('doctors')) {
            Schema::create('doctors', function (Blueprint $table) {
                $table->increments('doctor_id');
                $table->unsignedInteger('user_id');
                $table->string('specialty', 255)->nullable();
                $table->string('license_number', 100)->nullable();
                $table->string('hospital_affiliation', 255)->nullable();

                $table->foreign('user_id')->references('user_id')->on('users')->onDelete('cascade');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('doctors');
    }
};
