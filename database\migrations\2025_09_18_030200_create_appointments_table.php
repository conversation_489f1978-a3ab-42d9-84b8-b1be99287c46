<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (!Schema::hasTable('appointments')) {
            Schema::create('appointments', function (Blueprint $table) {
                $table->increments('appointment_id');
                $table->unsignedInteger('patient_id');
                $table->unsignedInteger('doctor_id');
                $table->dateTime('appointment_date');
                $table->enum('status', ['pending','confirmed','cancelled','completed'])->default('pending');
                $table->text('notes')->nullable();

                $table->foreign('patient_id')->references('patient_id')->on('patients')->onDelete('cascade');
                $table->foreign('doctor_id')->references('doctor_id')->on('doctors')->onDelete('cascade');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('appointments');
    }
};
