<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (!Schema::hasTable('vitals')) {
            Schema::create('vitals', function (Blueprint $table) {
                $table->increments('vital_id');
                $table->unsignedInteger('patient_id');
                $table->dateTime('recorded_at')->useCurrent();
                $table->integer('heart_rate')->nullable();
                $table->integer('blood_pressure_systolic')->nullable();
                $table->integer('blood_pressure_diastolic')->nullable();
                $table->float('glucose_level')->nullable();
                $table->float('spo2_level')->nullable();
                $table->float('weight')->nullable();
                $table->float('temperature')->nullable();

                $table->foreign('patient_id')->references('patient_id')->on('patients')->onDelete('cascade');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('vitals');
    }
};
