<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (!Schema::hasTable('medications')) {
            Schema::create('medications', function (Blueprint $table) {
                $table->increments('medication_id');
                $table->unsignedInteger('patient_id');
                $table->string('name', 255);
                $table->string('dosage', 100)->nullable();
                $table->string('frequency', 100)->nullable();
                $table->date('start_date')->nullable();
                $table->date('end_date')->nullable();
                $table->time('reminder_time')->nullable();

                $table->foreign('patient_id')->references('patient_id')->on('patients')->onDelete('cascade');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('medications');
    }
};
