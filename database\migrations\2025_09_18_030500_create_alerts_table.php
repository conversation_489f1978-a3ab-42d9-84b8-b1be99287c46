<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (!Schema::hasTable('alerts')) {
            Schema::create('alerts', function (Blueprint $table) {
                $table->increments('alert_id');
                $table->unsignedInteger('patient_id');
                $table->string('type', 100)->nullable();
                $table->text('message')->nullable();
                $table->enum('severity', ['low','medium','high'])->default('low');
                $table->dateTime('created_at')->useCurrent();
                $table->boolean('is_resolved')->default(false);

                $table->foreign('patient_id')->references('patient_id')->on('patients')->onDelete('cascade');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('alerts');
    }
};
