<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (!Schema::hasTable('ml_predictions')) {
            Schema::create('ml_predictions', function (Blueprint $table) {
                $table->increments('prediction_id');
                $table->unsignedInteger('patient_id');
                $table->enum('model_type', ['anomaly_detection','risk_classification','progression_forecast','clustering'])->nullable();
                $table->text('result')->nullable();
                $table->float('confidence_score')->nullable();
                $table->dateTime('predicted_at')->useCurrent();

                $table->foreign('patient_id')->references('patient_id')->on('patients')->onDelete('cascade');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('ml_predictions');
    }
};
