<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            if (!Schema::hasColumn('patients', 'insurance_provider')) {
                $table->string('insurance_provider', 255)->nullable()->after('emergency_contact');
            }
            if (!Schema::hasColumn('patients', 'policy_number')) {
                $table->string('policy_number', 100)->nullable()->after('insurance_provider');
            }
            if (!Schema::hasColumn('patients', 'health_history')) {
                $table->text('health_history')->nullable()->after('policy_number');
            }
        });
    }

    public function down(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            if (Schema::hasColumn('patients', 'health_history')) {
                $table->dropColumn('health_history');
            }
            if (Schema::hasColumn('patients', 'policy_number')) {
                $table->dropColumn('policy_number');
            }
            if (Schema::hasColumn('patients', 'insurance_provider')) {
                $table->dropColumn('insurance_provider');
            }
        });
    }
};
