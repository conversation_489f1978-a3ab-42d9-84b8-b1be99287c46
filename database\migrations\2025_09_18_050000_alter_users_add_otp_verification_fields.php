<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                if (!Schema::hasColumn('users', 'verification_code')) {
                    $table->string('verification_code', 10)->nullable()->after('remember_token');
                }
                if (!Schema::hasColumn('users', 'verification_code_expires_at')) {
                    $table->dateTime('verification_code_expires_at')->nullable()->after('verification_code');
                }
                if (!Schema::hasColumn('users', 'verification_attempts')) {
                    $table->unsignedTinyInteger('verification_attempts')->default(0)->after('verification_code_expires_at');
                }
                if (!Schema::hasColumn('users', 'last_verification_sent_at')) {
                    $table->dateTime('last_verification_sent_at')->nullable()->after('verification_attempts');
                }
            });
        }
    }

    public function down(): void
    {
        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                if (Schema::hasColumn('users', 'last_verification_sent_at')) {
                    $table->dropColumn('last_verification_sent_at');
                }
                if (Schema::hasColumn('users', 'verification_attempts')) {
                    $table->dropColumn('verification_attempts');
                }
                if (Schema::hasColumn('users', 'verification_code_expires_at')) {
                    $table->dropColumn('verification_code_expires_at');
                }
                if (Schema::hasColumn('users', 'verification_code')) {
                    $table->dropColumn('verification_code');
                }
            });
        }
    }
};
