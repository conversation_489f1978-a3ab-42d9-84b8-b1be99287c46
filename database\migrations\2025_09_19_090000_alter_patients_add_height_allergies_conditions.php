<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            if (!Schema::hasColumn('patients', 'height_cm')) {
                $table->float('height_cm')->nullable()->after('gender');
            }
            if (!Schema::hasColumn('patients', 'allergies')) {
                $table->text('allergies')->nullable()->after('health_history');
            }
            if (!Schema::hasColumn('patients', 'medical_conditions')) {
                $table->text('medical_conditions')->nullable()->after('allergies');
            }
        });
    }

    public function down(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            if (Schema::hasColumn('patients', 'medical_conditions')) {
                $table->dropColumn('medical_conditions');
            }
            if (Schema::hasColumn('patients', 'allergies')) {
                $table->dropColumn('allergies');
            }
            if (Schema::hasColumn('patients', 'height_cm')) {
                $table->dropColumn('height_cm');
            }
        });
    }
};
