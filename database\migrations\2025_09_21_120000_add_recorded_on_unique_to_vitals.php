<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('vitals', function (Blueprint $table) {
            if (!Schema::hasColumn('vitals', 'recorded_on')) {
                $table->date('recorded_on')->nullable()->after('recorded_at');
            }
        });

        // Backfill existing rows' recorded_on from recorded_at in Africa/Tunis
        // Note: portable approach via DB expression may vary by driver; app-level backfill can be added if needed.
        if (config('database.default')) {
            $connection = Schema::getConnection()->getDriverName();
            if ($connection === 'mysql') {
                DB::statement("UPDATE vitals SET recorded_on = CONVERT_TZ(recorded_at, '+00:00', '+01:00')");
            } else {
                // Fallback: use DATE(recorded_at) as UTC date when timezone conversion isn't available
                DB::statement("UPDATE vitals SET recorded_on = DATE(recorded_at)");
            }
        }

        Schema::table('vitals', function (Blueprint $table) {
            // Enforce one-entry-per-day-per-patient
            $table->unique(['patient_id', 'recorded_on'], 'vitals_patient_day_unique');
            $table->index(['patient_id', 'recorded_at'], 'vitals_patient_recordedat_idx');
        });
    }

    public function down(): void
    {
        Schema::table('vitals', function (Blueprint $table) {
            $table->dropUnique('vitals_patient_day_unique');
            $table->dropIndex('vitals_patient_recordedat_idx');
            $table->dropColumn('recorded_on');
        });
    }
};
