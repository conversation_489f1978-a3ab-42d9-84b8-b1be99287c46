<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('password_reset_attempts', function (Blueprint $table) {
            $table->id();
            $table->string('email');
            $table->string('ip', 45)->nullable();
            $table->enum('action', ['send','verify','success','fail']);
            $table->text('meta')->nullable();
            $table->timestamps();
            $table->index(['email','action']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('password_reset_attempts');
    }
};
