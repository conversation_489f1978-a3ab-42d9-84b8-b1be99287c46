<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (!Schema::hasTable('reports')) {
            Schema::create('reports', function (Blueprint $table) {
                $table->increments('report_id');
                $table->unsignedInteger('vital_id');
                $table->dateTime('generated_at')->useCurrent();
                $table->string('type', 50)->nullable(); // weekly, monthly, custom
                $table->text('summary')->nullable();
                $table->text('recommendations')->nullable();

                $table->foreign('vital_id')->references('vital_id')->on('vitals')->onDelete('cascade');
                $table->unique('vital_id'); // 1-to-1 with vitals
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('reports');
    }
};
