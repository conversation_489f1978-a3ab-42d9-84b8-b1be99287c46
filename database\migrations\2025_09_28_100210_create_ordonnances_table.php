<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (!Schema::hasTable('ordonnances')) {
            Schema::create('ordonnances', function (Blueprint $table) {
                $table->increments('ordonnance_id');
                $table->unsignedInteger('appointment_id')->nullable();
                $table->unsignedInteger('patient_id');
                $table->unsignedInteger('doctor_id');
                $table->dateTime('issued_at')->useCurrent();
                $table->text('content');
                $table->text('notes')->nullable();
                $table->string('status', 20)->default('active'); // active, revised, revoked
                $table->timestamps();

                $table->foreign('appointment_id')->references('appointment_id')->on('appointments')->onDelete('set null');
                $table->foreign('patient_id')->references('patient_id')->on('patients')->onDelete('cascade');
                $table->foreign('doctor_id')->references('doctor_id')->on('doctors')->onDelete('cascade');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('ordonnances');
    }
};
