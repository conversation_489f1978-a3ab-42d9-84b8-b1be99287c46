<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (!Schema::hasTable('tasks')) {
            Schema::create('tasks', function (Blueprint $table) {
                $table->increments('task_id');
                $table->unsignedInteger('patient_id')->nullable();
                $table->unsignedInteger('assigned_by')->nullable(); // doctor user_id
                $table->string('title');
                $table->text('description')->nullable();
                $table->enum('priority', ['low','medium','high'])->default('medium');
                $table->enum('status', ['open','in_progress','done','cancelled'])->default('open');
                $table->dateTime('due_at')->nullable();
                $table->timestamps();

                $table->foreign('patient_id')->references('patient_id')->on('patients')->onDelete('set null');
                $table->foreign('assigned_by')->references('user_id')->on('users')->onDelete('set null');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('tasks');
    }
};
