<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (!Schema::hasTable('lifestyles')) {
            Schema::create('lifestyles', function (Blueprint $table) {
                $table->increments('lifestyle_id');
                $table->unsignedInteger('patient_id');
                $table->enum('smoking_status', ['never','former','current'])->nullable();
                $table->enum('alcohol_consumption', ['none','occasionally','regularly'])->nullable();
                $table->enum('activity_level', ['sedentary','light','moderate','active'])->nullable();
                $table->integer('sleep_hours')->nullable();
                $table->text('diet_notes')->nullable();
                $table->timestamps();

                $table->foreign('patient_id')->references('patient_id')->on('patients')->onDelete('cascade');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('lifestyles');
    }
};
