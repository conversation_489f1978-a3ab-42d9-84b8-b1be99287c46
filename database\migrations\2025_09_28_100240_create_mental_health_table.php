<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (!Schema::hasTable('mental_health')) {
            Schema::create('mental_health', function (Blueprint $table) {
                $table->increments('mental_health_id');
                $table->unsignedInteger('patient_id');
                $table->integer('stress_level')->nullable(); // 1-10
                $table->integer('anxiety_level')->nullable(); // 1-10
                $table->integer('mood_level')->nullable(); // 1-10
                $table->text('notes')->nullable();
                $table->date('recorded_on')->nullable();
                $table->timestamps();

                $table->foreign('patient_id')->references('patient_id')->on('patients')->onDelete('cascade');
                $table->unique(['patient_id','recorded_on']);
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('mental_health');
    }
};
