<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (!Schema::hasTable('articles')) {
            Schema::create('articles', function (Blueprint $table) {
                $table->increments('article_id');
                $table->unsignedInteger('author_id')->nullable(); // users.user_id
                $table->string('title');
                $table->text('content');
                $table->string('slug')->unique();
                $table->boolean('published')->default(false);
                $table->timestamps();

                $table->foreign('author_id')->references('user_id')->on('users')->onDelete('set null');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('articles');
    }
};
