<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (!Schema::hasTable('forum_posts')) {
            Schema::create('forum_posts', function (Blueprint $table) {
                $table->increments('post_id');
                $table->unsignedInteger('author_id'); // users.user_id
                $table->string('title');
                $table->text('content');
                $table->unsignedInteger('parent_post_id')->nullable();
                $table->timestamps();

                $table->foreign('author_id')->references('user_id')->on('users')->onDelete('cascade');
                $table->foreign('parent_post_id')->references('post_id')->on('forum_posts')->onDelete('cascade');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('forum_posts');
    }
};
