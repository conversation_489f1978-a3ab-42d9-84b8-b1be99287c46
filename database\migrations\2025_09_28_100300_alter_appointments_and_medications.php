<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        // Alter appointments
        if (Schema::hasTable('appointments')) {
            Schema::table('appointments', function (Blueprint $table) {
                if (!Schema::hasColumn('appointments', 'appointment_type')) {
                    $table->string('appointment_type', 50)->nullable()->after('doctor_id');
                }
                // Keep existing status column as-is to avoid breaking changes; add if missing
                if (!Schema::hasColumn('appointments', 'status')) {
                    $table->string('status', 20)->default('scheduled');
                }
                if (!Schema::hasColumn('appointments', 'created_at')) {
                    $table->timestamps();
                }
            });
        }

        // Alter medications: link to appointment
        if (Schema::hasTable('medications')) {
            Schema::table('medications', function (Blueprint $table) {
                if (!Schema::hasColumn('medications', 'appointment_id')) {
                    $table->unsignedInteger('appointment_id')->nullable()->after('patient_id');
                    $table->foreign('appointment_id')->references('appointment_id')->on('appointments')->onDelete('set null');
                }
            });
        }

        // Optionally drop testimonials table if exists (prune unused)
        if (Schema::hasTable('testimonials')) {
            Schema::drop('testimonials');
        }
    }

    public function down(): void
    {
        // We won't revert status type change. Only drop added columns.
        if (Schema::hasTable('appointments')) {
            Schema::table('appointments', function (Blueprint $table) {
                if (Schema::hasColumn('appointments', 'appointment_type')) {
                    $table->dropColumn('appointment_type');
                }
                // timestamps may exist from start; skip dropping to avoid data loss in other flows
            });
        }

        if (Schema::hasTable('medications')) {
            Schema::table('medications', function (Blueprint $table) {
                if (Schema::hasColumn('medications', 'appointment_id')) {
                    $table->dropForeign(['appointment_id']);
                    $table->dropColumn('appointment_id');
                }
            });
        }
        // Do not recreate testimonials on down.
    }
};
