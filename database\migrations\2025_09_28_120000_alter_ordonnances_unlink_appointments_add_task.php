<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (Schema::hasTable('ordonnances')) {
            Schema::table('ordonnances', function (Blueprint $table) {
                if (Schema::hasColumn('ordonnances', 'appointment_id')) {
                    // Drop FK then column
                    try { $table->dropForeign(['appointment_id']); } catch (\Throwable $e) {}
                    $table->dropColumn('appointment_id');
                }
                if (!Schema::hasColumn('ordonnances', 'task_id')) {
                    $table->unsignedInteger('task_id')->nullable()->after('doctor_id');
                    $table->foreign('task_id')->references('task_id')->on('tasks')->onDelete('set null');
                }
            });
        }
    }

    public function down(): void
    {
        if (Schema::hasTable('ordonnances')) {
            Schema::table('ordonnances', function (Blueprint $table) {
                if (Schema::hasColumn('ordonnances', 'task_id')) {
                    try { $table->dropForeign(['task_id']); } catch (\Throwable $e) {}
                    $table->dropColumn('task_id');
                }
                // Do not restore appointment_id
            });
        }
    }
};
