<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create or keep a known API test user (idempotent)
        \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'API Tester',
                'password_hash' => \Illuminate\Support\Facades\Hash::make('secret'),
                'role' => 'patient',
                'email_verified_at' => now(),
            ]
        );

        // Create admin user
        $this->call(AdminUserSeeder::class);
    }
}
