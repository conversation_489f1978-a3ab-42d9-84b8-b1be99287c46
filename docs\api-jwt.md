# API JWT Quickstart

Base URL: `/api`

Set env:
- `JWT_SECRET` (required in production)
- Optional: `JWT_TTL` (minutes), `JWT_REFRESH_TTL` (minutes)

## Endpoints

- `POST /v1/auth/login` — returns `access_token`
- `GET /v1/auth/me` — user info (requires Bearer)
- `POST /v1/auth/refresh` — new `access_token` (requires Bearer)
- `POST /v1/auth/logout` — invalidates current token (requires Bearer)
- `GET /v1/vitals/feed` — latest vitals (requires Bearer)

## Examples

```bash
# Login
curl -sS -X POST "http://localhost/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"secret"}'

# Save token
TOKEN="<paste access_token>"

# Me
curl -sS http://localhost/api/v1/auth/me -H "Authorization: Bearer $TOKEN"

# Feed
curl -sS http://localhost/api/v1/vitals/feed -H "Authorization: Bearer $TOKEN"

# Refresh
curl -sS -X POST http://localhost/api/v1/auth/refresh -H "Authorization: Bearer $TOKEN"

# Logout (invalidates current token)
curl -sS -X POST http://localhost/api/v1/auth/logout -H "Authorization: Bearer $TOKEN"
```

Notes:
- Logout uses a denylist keyed by token `jti` stored in cache until token expiration.
- Ensure your cache store is configured (default file cache works for single-instance). For multi-instance, use Redis.
