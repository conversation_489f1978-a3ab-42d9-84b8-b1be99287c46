# Development Journal

Chronological steps we implemented in this project.

## 1) Landing Page & Interactions
- Built responsive landing with animated canvases (particles, ECG) and heart particle.
- Implemented dark mode toggle and scroll-reveal animations.

## 2) Auth UI
- Created register and login pages with role switch (patient/doctor) and adaptive fields.

## 3) Email Verification & Onboarding Plan
- Enabled `MustVerifyEmail` on `User` and added verification routes/views.
- Designed onboarding flow to capture baseline vitals after verification.

## 4) Initial Vitals (Deprecated)
- Prototyped `initial_vitals` migration/model/controller.
- Later deprecated in favor of storing baseline vitals in `vitals`.

## 5) MySQL Environment & Driver
- Encountered `could not find driver` (PDO MySQL missing).
- Guided enabling `pdo_mysql` in PHP; verified with `php -m` and `php --ri pdo_mysql`.
- Temporarily switched cache/queue to file/sync to avoid DB dependency during artisan operations.

## 6) Custom Schema Alignment
- Received `hct.sql` with INT PKs, `users.user_id`, `password_hash`, `role` enum.
- Updated models: `User` (custom PK, password_hash), `Patient`, `Doctor`, `Vital`.
- Reworked controllers and views to match schema (onboarding uses `vitals`).

## 7) Full Schema via Migrations
- Added migrations for: patients, doctors, appointments, vitals, medications, alerts, ml_predictions, testimonials, logs.
- Guarded default `users` migration; added alter migration for `email_verified_at` & `remember_token`.
- Marked `initial_vitals` migration as no-op to avoid FK clash.

## 8) Bug Fixes & Perf
- Fixed JS error accessing `__running` on pages without `#ecg-canvas`.
- Optimized Composer autoload (`composer dump-autoload -o`).

## 9) Patient Insurance & History
- Added columns and UI wiring for `insurance_provider`, `policy_number`, `health_history` on `patients`.

## 10) Finalize & Serve
- Migrations run successfully against MySQL.
- App serves via `php artisan serve` with the animated landing and auth flow.
