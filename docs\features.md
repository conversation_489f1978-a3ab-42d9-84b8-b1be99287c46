# Features

## Landing & UI
- Animated hero with canvas particles and ECG heartbeat
- NextParticle-based heart animation
- Dark mode toggle (persisted), a11y-friendly dialogs and focus traps

## Auth & Verification
- Register/Login/Logout
- Email verification (MustVerifyEmail) with notice/resend

## Onboarding
- Role selection (patient/doctor)
- Patient-only baseline vitals capture (stored in `vitals`)
- Redirect logic based on `hasBaselineVitals()`

## Data Model
- Users with roles; patient/doctor profiles via separate tables
- Vitals, Appointments, Medications, Alerts, ML Predictions, Testimonials, Logs

## Migrations
- Schema implemented via migrations with checks to avoid conflicts with existing DBs
- Default `users` migration guarded; alter migration adds missing auth columns

## Tooling
- Vite bundling; Composer autoload optimized; Laravel artisan tasks
