# Overview

Healthcare Tracker is a Laravel application with a modern landing page and onboarding flow for patients and doctors. It includes:

- Animated landing experience (canvas particles, heart particle, ECG line)
- Authentication (register/login/logout)
- Email verification (MustVerifyEmail)
- Role-based onboarding (patient baseline vitals capture)
- MySQL persistence aligned to a custom schema

## Architecture

- Laravel (Blade, Eloquent, Routing, Middleware)
- Vite for assets; vanilla JS modules for animations
- Models aligned to custom schema (INT primary keys, enum roles)

## Data Flow Highlights

- Register → Verify email → Welcome → Patient submits baseline vitals → Dashboard
- Patients and Doctors have separate profile tables linked to `users`
- Vitals recorded per patient; baseline uses first row in `vitals`
