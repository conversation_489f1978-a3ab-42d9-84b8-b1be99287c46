# Project Structure and Functionalities

This document gives a complete, high-level map of the HealthCare Tracker application: folders, routes, controllers, middleware, models, database schema, core flows (auth, onboarding, vitals, dashboard, profile, password reset), and the JWT API.

## Summary

- <PERSON>vel + Blade app with dark/light theme, onboarding, profile with avatar, daily vitals policy, Blade dashboard, two-step email code password reset, and a JWT API.
- Time policy: daily vitals enforce exactly one row per patient per local day in Africa/Tunis via `recorded_on` uniqueness; timestamps stored UTC.
- JWT: HS256 via `firebase/php-jwt`, custom `JwtService`, denylist on logout.

## Tech Stack

- Framework: Laravel (Routing, Controllers, Middleware, Blade, Eloquent, Validation)
- Frontend: Blade views, CSS variables for theming, vanilla JS for small interactions
- Auth: Session-based for web; custom email OTP verification; JWT for API
- Storage: Public disk for profile photos (`php artisan storage:link`)
- DB: SQLite/MySQL compatible schema defined via migrations

## Key Folders

- `app/Http/Controllers` — Web and API controllers
  - `AuthController.php` (web auth + email OTP verification)
  - `Api/AuthController.php` (API JWT auth)
  - `DashboardController.php` (dashboard UI + feeds + CSV export)
  - `ProfileController.php` (profile view + account/patient update + photo upload)
  - `OnboardingController.php` (guided onboarding flow)
  - `PasswordResetController.php` (two-step reset: send code → verify → new password → success)
  - `DoctorsController.php`, `NotificationsController.php`, `SettingsController.php` (section pages)
  - `ThemeController.php` (theme helpers/toggle if used)
- `app/Http/Middleware` — Request guards and validators
  - `AuthenticateJwt.php` (protects API routes with JWT)
  - `RedirectIfOnboardingIncomplete.php` (alias `onboarding.required`)
  - `ValidateLogin.php`, `ValidateRegister.php`, `ValidateVitals.php`, `ValidateOnboarding.php`
  - `SecurityHeaders.php`, `BasicHeaders.php`, `CacheControlAssets.php`
- `app/Models` — Eloquent models
  - `User`, `Patient`, `Doctor`, `Vital`, `Appointment`, `Medication`, `Alert`, `MlPrediction`, `Testimonial`, `ActivityLog`, `InitialVital` (legacy), etc.
- `app/Services/JwtService.php` — Token creation/validation helpers
- `routes/web.php` — Web pages and JSON feeds (session auth)
- `routes/api.php` — JWT API endpoints
- `resources/views` — Blade pages (auth, onboarding, dashboard, profile, etc.)
- `config/jwt.php` — JWT config (secret, issuer, ttl)
- `database/migrations` — Full schema definition
- `docs/` — Additional docs: `setup.md`, `api-jwt.md`, `features.md`, `overview.md`, `schema.md`

## Web Routes (session auth)

See `routes/web.php` for the full list. Key paths below with names and middleware.

- Public
  - `GET /` → `welcome` landing (animated UI)
  - Password reset (guest only)
    - `GET /password/forgot` → `PasswordResetController@showForgotForm` (`password.forgot`)
    - `POST /password/forgot` → `PasswordResetController@sendCode` (`password.forgot.send`)
    - Legacy: `GET|POST /password/reset` combined form (`password.reset`, `password.reset.post`)
    - Two-step: `GET|POST /password/verify` (`password.verify`, `password.verify.post`)
    - Two-step: `GET|POST /password/new` (`password.new`, `password.new.post`)
    - Success: `GET /password/success` (`password.success`)
- Authentication
  - `GET /login` → `AuthController@showLogin` (`login`)
  - `POST /login` → `AuthController@login` (`validate.login`, name `login.attempt`)
  - `GET /register` → `AuthController@showRegister` (`register`)
  - `POST /register` → `AuthController@register` (`validate.register`, name `register.store`)
  - `POST /logout` → `AuthController@logout` (`auth`)
- Email verification (via OTP; requires `auth`)
  - `GET /email/verify` → show OTP form (`verification.otp.form`)
  - `POST /email/verify` → verify OTP (`verification.otp.verify`)
  - `POST /email/verification-resend` → resend OTP (`verification.otp.resend`)
- Onboarding (requires `auth`, `verified`)
  - `GET /onboarding` → `OnboardingController@show` (`onboarding.show`)
  - `POST /onboarding` → `OnboardingController@store` (`validate.vitals`, name `onboarding.store`)
  - `GET|POST /welcome-onboarding` → redirect to guided flow (preserve route names)
  - `POST /onboarding/skip` → set one-time skip flag then redirect to dashboard (`onboarding.skip`)
- App (requires `auth`, `verified`, `onboarding.required`)
  - `GET /dashboard` → `DashboardController@index` (`dashboard`)
  - `GET /vitals/feed` → `DashboardController@feed` (`vitals.feed`) JSON for dashboard
  - `GET /dashboard/export/csv` → `DashboardController@exportCsv` (`dashboard.export.csv`)
  - `GET /doctors` → `DoctorsController@index` (`doctors.index`)
  - `GET /notifications` → `NotificationsController@index` (`notifications.index`)
  - `GET /settings` → `SettingsController@index` (`settings.index`)
- Profile (requires `auth`)
  - `GET /profile` → `ProfileController@show` (`profile.show`)
  - `PUT /profile/account` → `ProfileController@updateAccount` (`profile.update.account`)
  - `PUT /profile/patient` → `ProfileController@updatePatient` (`profile.update.patient`)
  - `POST /profile/photo` → `ProfileController@updatePhoto` (`profile.update.photo`)

## API Routes (JWT)

Base path: `/api`

- `POST /v1/auth/login` → `Api\AuthController@login`
- Protected (middleware: `jwt`):
  - `GET /v1/auth/me` → `Api\AuthController@me`
  - `POST /v1/auth/refresh` → `Api\AuthController@refresh`
  - `POST /v1/auth/logout` → `Api\AuthController@logout` (denylist current token)
  - `GET /v1/vitals/feed` → `DashboardController@feed` (same payload as web feed)

## Controllers — Responsibilities

- `AuthController` (web)
  - `showLogin`, `login` (validation via `validate.login`)
  - `showRegister`, `register` (validation via `validate.register`)
  - `logout`
  - Email OTP verification: `showOtpForm`, `verifyOtp`, `resendOtp` (sets `email_verified_at` when successful)
- `Api/AuthController` (API)
  - `login` (issue `access_token`, TTL from `config/jwt.php`)
  - `me`, `refresh`, `logout` (denylist)
- `PasswordResetController`
  - Two-step reset: `sendCode` → `verify` → `setNewPassword` → `success`
  - Hashes codes, throttles and quotas, logs attempts best-effort; success view with animation
- `OnboardingController`
  - Guided onboarding; for patients, captures baseline vitals using same validator as daily vitals
  - Supports one-time session skip (`onboarding_skip_once`)
- `DashboardController`
  - `index` renders Blade dashboard (24h series, 7-day aggregates, trends, reminder after noon if none today)
  - `feed` returns JSON for charts (also used by API)
  - `exportCsv` downloads CSV of recent vitals
- `ProfileController`
  - `show` profile with vitals history (filters/pagination)
  - `updateAccount` (name, photo url, theme preference) and `updatePatient` (demographics, health)
  - `updatePhoto` handles file upload to `public` disk
- `DoctorsController`, `NotificationsController`, `SettingsController`
  - Simple section index pages

## Middleware — Purpose & Use

- `auth`, `verified` — standard guards; verified is satisfied by OTP email verification flow
- `onboarding.required` (`RedirectIfOnboardingIncomplete`) — prevents access to app pages until onboarding finished (unless session skip flag is set)
- `jwt` (`AuthenticateJwt`) — validates Bearer token; sets the authenticated user for API
- `validate.login`, `validate.register`, `validate.vitals`, `validate.onboarding` — request validators
- `SecurityHeaders`, `BasicHeaders`, `CacheControlAssets` — security and caching headers for responses

## Models & Relationships (typical)

- `User` (`user_id` PK) — hasOne `Patient`, hasOne `Doctor`, hasMany `ActivityLog`, hasMany `Testimonial`
- `Patient` (`patient_id` PK, FK `user_id`) — belongsTo `User`; hasMany `Vital`, `Appointment`, `Medication`, `Alert`, `MlPrediction`
- `Doctor` (`doctor_id` PK, FK `user_id`) — belongsTo `User`; hasMany `Appointment`
- `Vital` (`vital_id` PK, FK `patient_id`) — belongsTo `Patient`
- `Appointment`, `Medication`, `Alert`, `MlPrediction`, `Testimonial`, `ActivityLog` — standard FKs to `patient_id` or `user_id` as per table

## Database Schema (from migrations)

Core tables and important columns/constraints:

- `users` (PK: `user_id` INT AI)
  - `name`, `email` (unique), `password_hash`, `role` (enum: patient|doctor|admin)
  - Email verification/token fields; notification preferences; `photo_url`; `theme_preference`
  - Timestamps; `remember_token` (optional)
- `patients` (PK: `patient_id`; FK `user_id` → `users.user_id`)
  - `date_of_birth`, `gender`, `address`, `phone_number`, `emergency_contact`
  - `insurance_provider`, `policy_number`, `health_history`
  - later: `height_cm`, `allergies`, `conditions`
- `doctors` (PK: `doctor_id`; FK `user_id` → `users.user_id`)
  - `specialty`, `license_number`, `hospital_affiliation`
- `appointments` (PK: `appointment_id`; FKs `patient_id`, `doctor_id`)
  - `appointment_date`, `status`, `notes`
- `vitals` (PK: `vital_id`; FK `patient_id`)
  - `recorded_at` (UTC), `recorded_on` (DATE, Africa/Tunis local day)
  - `heart_rate`, `blood_pressure_systolic`, `blood_pressure_diastolic`, `glucose_level`, `spo2_level`, `weight`, `temperature`
  - Unique: (`patient_id`, `recorded_on`) — one row per local day
- `medications` (PK: `medication_id`; FK `patient_id`)
  - `name`, `dosage`, `frequency`, `start_date`, `end_date`, `reminder_time`
- `alerts` (PK: `alert_id`; FK `patient_id`)
  - `type`, `message`, `severity`, `is_resolved`, timestamps
- `ml_predictions` (PK: `prediction_id`; FK `patient_id`)
  - `model_type`, `result` (JSON or text), `confidence_score`, `predicted_at`
- `testimonials` (PK: `testimonial_id`; FK `user_id`)
  - `message`, `rating`, timestamps
- `logs` / `activity_logs` (PK: `log_id`; FK `user_id`)
  - `action`, `description`, timestamps
- `password_reset_attempts`
  - Audits reset attempts (email, ip, status, created_at) — best-effort writes from controller

Note: An earlier `initial_vitals` table exists by migration but baseline vitals are stored in `vitals` for consistency.

## Core Flows

### Register & Email Verification (OTP)

1) `GET /register` → form → `POST /register` with `validate.register`
2) User logs in or is redirected to `GET /email/verify` (OTP form)
3) `POST /email/verify` checks code; on success sets `email_verified_at`
4) Verified users proceed to onboarding or dashboard

### Login/Logout

- `GET /login` → form → `POST /login` with `validate.login`
- `POST /logout` ends session

### Password Reset (Two-step via Email Code)

1) `GET /password/forgot` → submit email (`POST /password/forgot`)
2) `GET|POST /password/verify` → enter 6-digit code (TTL + attempt limits)
3) `GET|POST /password/new` → set new password; shows `/password/success` with animation

Security: hashed codes, throttle + quotas, guarded audit logging; generic user messaging.

### Onboarding

- `GET /onboarding` (auth+verified) shows role-tailored steps; patient path captures baseline vitals
- `POST /onboarding` validates via `validate.vitals` and persists
- One-time skip via `POST /onboarding/skip` (session flag)

### Daily Vitals Policy

- Server computes `recorded_on` for Africa/Tunis and enforces uniqueness with (`patient_id`, `recorded_on`)
- Stores `recorded_at` in UTC
- Dashboard prompts a reminder after noon local time if missing today’s entry

### Dashboard

- Blade-only UI; shows current metrics, last 24h series, 7-day aggregates, trends
- Data source: `GET /vitals/feed` JSON; export via `GET /dashboard/export/csv`

### Profile

- `GET /profile` shows account + patient details and a vitals history timeline
- Update endpoints: `PUT /profile/account`, `PUT /profile/patient`
- `POST /profile/photo` uploads avatar to `public` disk (`storage/app/public` → `public/storage`)

### Theme Toggle

- UI theme (dark/light) controlled by CSS variables and user preference (`users.theme_preference`)
- Inputs and cards adapt using `--color-text`, `--color-surface`, etc.

## JWT Auth (API)

- Config: `config/jwt.php` — `secret`, `issuer`, `audience`, `ttl`, `refresh_ttl`
- Service: `app/Services/JwtService.php` — creates/parses HS256 tokens with `jti`
- Middleware: `AuthenticateJwt` — validates Bearer, checks denylist, attaches user
- Routes under `/api/v1/auth/*` and protected feeds under `/api/v1/vitals/*`
- Logout denylists current token until expiry (cache-backed)

## Setup & Run

See `README.md` and `docs/setup.md` for Windows PowerShell quickstart and environment details. Typical flow:

```powershell
composer install
copy .env.example .env
php artisan key:generate
php artisan migrate
php artisan storage:link
php artisan serve
# In another terminal
npm install
npm run dev
```

For API: set `JWT_SECRET` in `.env` (strong random string) and consult `docs/api-jwt.md` for curl examples.

## Notes

- SQLite (dev) and MySQL (prod) are supported by the migrations; adjust `.env` accordingly
- If adding new public uploads, ensure the storage symlink is present
- For multi-instance deployments, use Redis for cache-based JWT denylist
