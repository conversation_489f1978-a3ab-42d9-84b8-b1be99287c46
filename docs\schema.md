# Schema Mapping

This app maps to the custom MySQL schema provided in `hct.sql`. We implemented the schema via Laravel migrations with guarded checks.

## Core Tables
- `users`
  - PK: `user_id` (INT, AI)
  - Columns: `name`, `email`, `password_hash`, `role` (enum: patient, doctor, admin), `email_verified_at?`, `remember_token?`, timestamps
- `patients`
  - PK: `patient_id`
  - FK: `user_id` → `users.user_id`
  - Profile: `date_of_birth`, `gender`, `address`, `phone_number`, `emergency_contact`
  - Insurance: `insurance_provider`, `policy_number`, `health_history`
- `doctors`
  - PK: `doctor_id`
  - FK: `user_id` → `users.user_id`
  - Profile: `specialty`, `license_number`, `hospital_affiliation`
- `appointments`
  - PK: `appointment_id`
  - FKs: `patient_id` → patients, `doctor_id` → doctors
  - `appointment_date`, `status`, `notes`
- `vitals`
  - PK: `vital_id`
  - FK: `patient_id` → patients
  - Metrics: `recorded_at`, `heart_rate`, `blood_pressure_systolic`, `blood_pressure_diastolic`, `glucose_level`, `spo2_level`, `weight`, `temperature`
- `medications`
  - PK: `medication_id`
  - FK: `patient_id` → patients
  - `name`, `dosage`, `frequency`, `start_date`, `end_date`, `reminder_time`
- `alerts`
  - PK: `alert_id`
  - FK: `patient_id` → patients
  - `type`, `message`, `severity`, `created_at`, `is_resolved`
- `ml_predictions`
  - PK: `prediction_id`
  - FK: `patient_id` → patients
  - `model_type`, `result`, `confidence_score`, `predicted_at`
- `testimonials`
  - PK: `testimonial_id`
  - FK: `user_id` → users
  - `message`, `rating`, `created_at`
- `logs`
  - PK: `log_id`
  - FK: `user_id` → users
  - `action`, `description`, `created_at`

## Eloquent Configuration
- Models use custom primary keys and `$timestamps = false` where the table lacks standard timestamps.
- The deprecated `initial_vitals` migration is a no-op; baseline vitals use `vitals`.
