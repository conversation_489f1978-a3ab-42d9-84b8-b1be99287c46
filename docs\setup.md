# Setup & Run

## Requirements
- PHP 8.2+ (Thread Safe) with extensions: `pdo_mysql`, `openssl`, `mbstring`, `tokenizer`, `xml`
- Composer
- Node 18+
- MySQL 8+ (or MariaDB compatible)

## Installation
```powershell
# From project root
composer install
cp .env.example .env  # or create your .env
php artisan key:generate

npm install
npm run build  # or npm run dev
```

## Database
Set your MySQL credentials in `.env`:
```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=healthcare_tracker
DB_USERNAME=root
DB_PASSWORD=
```
Enable `pdo_mysql` in your active `php.ini`:
```
extension=pdo_mysql
extension_dir="C:\\Program Files\\php-8.3.12-Win32-vs16-x64\\ext"
```
Then:
```powershell
php artisan migrate
```

## Serve
```powershell
php artisan serve
```
Visit `http://127.0.0.1:8000`.

## Dev Tips
- If using OneDrive path, autoload may be slow on first hit; optimize: `composer dump-autoload -o`
- For quicker iteration: `npm run dev`
