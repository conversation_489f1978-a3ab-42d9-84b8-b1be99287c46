# Troubleshooting Guide

Common issues and fixes for this Laravel + MySQL project on Windows.

## 1) PDO MySQL: "could not find driver"
Symptoms: Running migrations or app shows SQLSTATE[HY000] [2002] or "could not find driver".

Fix:
- Enable `pdo_mysql` extension in the active `php.ini` (CLI one). On Windows:
  - Find active php.ini: `php --ini`
  - Open the displayed `Loaded Configuration File`
  - Ensure the line `extension=pdo_mysql` is present (remove `;` if commented)
  - Restart terminals/VS Code
- Verify:
  - `php -m | findstr /i mysql`
  - `php --ri pdo_mysql`

## 2) Migrations refer to wrong PK (users.id vs users.user_id)
Symptoms: Foreign key errors referencing `users.id` when your schema uses `users.user_id`.

Fix:
- Ensure your migrations and models use `user_id` as PK:
  - In models: set `protected $primaryKey = 'user_id';` and disable increments/casts if needed.
  - In migrations: use `unsignedInteger('user_id')` for FKs to `users.user_id`.
- If a migration already created `users.id`, guard it (check `Schema::hasColumn`) or adjust to use `user_id`.

## 3) FK errors during migrate
Symptoms: "errno: 150 Foreign key constraint is incorrectly formed".

Fix:
- Check that FK and referenced columns have the same type/unsigned and length.
- Create parent tables before child tables; order your migrations or split into separate timestamps.
- On SQLite or MySQL with strict mode, ensure engine supports FKs (`InnoDB`).

## 4) Cache/Config stale after changes
Symptoms: Changes in `.env` or config not reflected.

Fix:
- `php artisan config:clear` then `php artisan cache:clear`
- For routing issues: `php artisan route:clear`

## 5) Windows-specific path or permission issues
- Run terminals as Administrator when binding to low ports or writing to certain directories.
- Avoid non-ASCII characters in project path if PHP extensions act up.
- If using OneDrive folder, ensure sync isn't locking files during `migrate`.

## 6) Email verification not sending
- In `.env`, set correct mailer settings (e.g., Mailtrap for dev) and run `php artisan queue:work` if using async mail.
- For dev, you can switch `QUEUE_CONNECTION=sync`.

## 7) JS errors on pages without canvases
- Ensure scripts check for element existence before accessing or starting animations.

If the problem persists, capture the exact error message and the migration or file involved to triage further.