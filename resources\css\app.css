/* =====================
     Healthcare Tracker Landing Page Styles
     Mobile-first, accessible, animated
     ===================== */

:root {
    --color-bg: #ffffff;
    --color-bg-alt: #f5f9fb;
    --color-surface: #ffffff;
    --color-surface-alt: #eef5f9;
    --color-border: #d9e3ea;
    /* Swapped: green primary in light mode, blue accent */
    --color-primary: #38b000; /* green */
    --color-primary-accent: #00b4d8; /* mid accent for gradients */
    --color-accent: #0077b6; /* blue */
    --color-text: #1b2533;
    --color-text-soft: #435266;
    --color-danger: #e63946;
    --radius-sm: .4rem;
    --radius-md: .8rem;
    --radius-lg: 1.6rem;
    --radius-pill: 999px;
    --shadow-sm: 0 1px 2px rgba(0,0,0,.06),0 1px 3px rgba(0,0,0,.08);
    --shadow-md: 0 4px 12px -2px rgba(0,0,0,.08),0 2px 4px rgba(0,0,0,.06);
    --shadow-lg: 0 10px 32px -8px rgba(0,34,68,.25),0 4px 12px rgba(0,34,68,.15);
    --gradient-primary: linear-gradient(135deg,var(--color-primary) 0%,var(--color-primary-accent) 50%,var(--color-accent) 100%);
    --gradient-accent: radial-gradient(circle at 30% 30%,var(--color-primary-accent) 0%,rgba(0,180,216,0) 60%),radial-gradient(circle at 70% 60%,var(--color-primary) 0%,rgba(56,176,0,0) 55%);
    --font-sans: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', Arial, sans-serif;
    --trans-fast: .18s cubic-bezier(.4,0,.2,1);
    --trans-base: .35s cubic-bezier(.4,0,.2,1);
    --container-max: 1280px;
    color-scheme: light;
}

@media (prefers-reduced-motion: reduce) {
    * { animation-duration: .001ms !important; animation-iteration-count: 1 !important; transition-duration: .001ms !important; }
}

.dark :root, :root.dark { color-scheme: dark; }
body.dark, .dark body, :root.dark body { --color-bg:#0d1116; --color-bg-alt:#141b23; --color-surface:#161f28; --color-surface-alt:#1f2a35; --color-border:#273544; --color-text:#e6eef5; --color-text-soft:#93a3b5; --shadow-lg:0 10px 32px -8px rgba(0,0,0,.6),0 4px 12px rgba(0,0,0,.4); }
/* Swap roles in dark: blue primary, green accent */
body.dark { --color-primary:#0077b6; --color-accent:#38b000; }

html,body { margin:0; padding:0; font-family:var(--font-sans); background:var(--color-bg); color:var(--color-text); -webkit-font-smoothing:antialiased; }
body { min-height:100dvh; line-height:1.55; }

.skip-link { position:absolute; left:-10000px; top:auto; width:1px; height:1px; overflow:hidden; }
.skip-link:focus { left:50%; top:1rem; transform:translateX(-50%); width:auto; height:auto; background:var(--color-primary); color:#fff; padding:.75rem 1rem; border-radius:var(--radius-sm); z-index:1000; box-shadow:var(--shadow-md); }

/* Layout helpers */
.section { position:relative; padding: clamp(4rem, 7vw, 7rem) 1.25rem; }
.section:nth-of-type(even) { background:var(--color-bg-alt); }
.section__head { max-width:720px; margin:0 auto 2.5rem; text-align:center; }
.section__title { font-size:clamp(1.75rem,4vw,2.75rem); line-height:1.15; margin:0 0 1rem; letter-spacing:-.02em; }
.section__subtitle { margin:0; font-size:clamp(.95rem,1.2vw,1.05rem); color:var(--color-text-soft); }

/* Header */
.site-header { position:sticky; top:0; backdrop-filter:blur(18px); -webkit-backdrop-filter:blur(18px); background:rgba(255,255,255,.75); border-bottom:1px solid var(--color-border); z-index:50; padding:.75rem 1.25rem; display:flex; justify-content:center; }
body.dark .site-header { background:rgba(13,17,22,.75); }
.nav { width:100%; max-width:var(--container-max); display:flex; align-items:center; gap:2rem; }
.nav__logo { font-weight:700; font-size:1.15rem; display:flex; align-items:center; gap:.4rem; letter-spacing:.5px; }
.nav__logo .accent { background:linear-gradient(90deg,var(--color-primary-accent),var(--color-primary)); -webkit-background-clip:text; background-clip:text; color:transparent; }
.nav__menu { list-style:none; margin:0 0 0 auto; padding:0; display:flex; gap:1rem; align-items:center; }
.nav__menu a, .nav__menu button.btn--ghost { text-decoration:none; padding:.55rem .95rem; border-radius:var(--radius-pill); font-size:.9rem; font-weight:500; color:var(--color-text-soft); display:inline-flex; align-items:center; gap:.35rem; }
.nav__menu a:hover, .nav__menu a:focus-visible, .nav__menu button.btn--ghost:hover { background:var(--color-surface-alt); color:var(--color-text); }
body.dark .nav__menu a:hover { background:var(--color-surface-alt); }
/* Active page glow/pill for top nav */
.nav__menu a.is-active, .nav__menu a[aria-current="page"], .nav__menu button.btn--ghost.is-active {
    position:relative; background:var(--color-surface-alt); color:var(--color-text);
}
.nav__menu a.is-active::after, .nav__menu a[aria-current="page"]::after, .nav__menu button.btn--ghost.is-active::after {
    content:""; position:absolute; inset:0; border-radius:var(--radius-pill);
    box-shadow: 0 0 0 6px color-mix(in srgb, var(--color-primary) 16%, transparent), 0 0 14px -4px var(--color-primary);
    pointer-events:none; opacity:.85;
}
.nav__toggle { display:none; background:none; border:0; cursor:pointer; width:2.5rem; height:2.5rem; border-radius:var(--radius-pill); position:relative; }
.nav__toggle .line { position:absolute; left:25%; right:25%; height:2px; background:var(--color-text); transition:var(--trans-base); }
.nav__toggle .line:nth-child(1){ top:32%; }
.nav__toggle .line:nth-child(2){ top:50%; }
.nav__toggle .line:nth-child(3){ top:68%; }
.nav__toggle[aria-expanded="true"] .line:nth-child(1){ transform:translateY(9px) rotate(45deg); }
.nav__toggle[aria-expanded="true"] .line:nth-child(2){ opacity:0; }
.nav__toggle[aria-expanded="true"] .line:nth-child(3){ transform:translateY(-9px) rotate(-45deg); }

/* Hero */
.hero { display:grid; gap:3rem; max-width:var(--container-max); margin:0 auto; align-items:center; }
@media (min-width: 900px) { .hero { grid-template-columns: repeat(2,1fr); min-height: clamp(70vh, 92vh, 880px); } }
.hero__content { position:relative; z-index:2; }
.hero__title { font-size:clamp(2.2rem,5vw,3.6rem); margin:0 0 1.25rem; font-weight:800; line-height:1.05; }
.gradient-text { background:linear-gradient(90deg,var(--color-accent),var(--color-primary-accent),var(--color-primary)); -webkit-background-clip:text; background-clip:text; color:transparent; }
.hero__subtitle { font-size:clamp(1rem,1.3vw,1.15rem); max-width:52ch; color:var(--color-text-soft); margin:0 0 1.75rem; }
.hero__actions { display:flex; flex-wrap:wrap; gap:1rem; align-items:center; }
.hero__stats { display:flex; gap:2rem; padding:0; margin:2.25rem 0 0; list-style:none; }
.hero__stats li { display:flex; flex-direction:column; }
.stat__num { font-size:1.9rem; font-weight:700; background:linear-gradient(90deg,var(--color-accent),var(--color-primary)); -webkit-background-clip:text; background-clip:text; color:transparent; }
.stat__label { font-size:.75rem; text-transform:uppercase; letter-spacing:1px; font-weight:600; color:var(--color-text-soft); }
.hero__visual { position:relative; }
/* Raised further (additional 70px) */
.heart-visual { position:relative; width:460px; max-width:100%; aspect-ratio:1/1; margin:0 auto; transform:translateY(-100px); }
@media (max-width: 640px){
    /* Mobile adjusted upward similarly but slightly less to avoid clipping */
    .heart-visual { transform:translateY(-85px); }
}
.heart-visual img#valentines { display:block; width:100%; height:100%; object-fit:contain; opacity:0; pointer-events:none; }
.heart-visual canvas#heart-canvas { position:absolute; inset:0; width:100% !important; height:100% !important; }
.heart-social { position:absolute; top:6px; right:6px; display:flex; gap:.4rem; z-index:5; }
.heart-social__link { background:rgba(0,0,0,.55); color:#fff; text-decoration:none; font-size:.6rem; padding:.35rem .45rem; border-radius:.5rem; font-weight:600; letter-spacing:.5px; backdrop-filter:blur(4px); -webkit-backdrop-filter:blur(4px); }
body.dark .heart-social__link { background:rgba(255,255,255,.15); }

.device-mock { width:260px; max-width:100%; aspect-ratio:9/18; background:var(--color-surface); border-radius:2rem; box-shadow: inset 0 0 0 2px var(--color-border), var(--shadow-lg); padding:.9rem; margin-inline:auto; position:relative; }
.device-mock__screen { width:100%; height:100%; background:linear-gradient(180deg,#0d1b2a,#1b263b); border-radius:1.4rem; position:relative; overflow:hidden; display:flex; flex-direction:column; justify-content:flex-end; padding:1rem; }
.vitals { display:grid; grid-template-columns: repeat(3,1fr); gap:.75rem; }
.vital { background:rgba(255,255,255,.07); color:#fff; padding:.55rem .6rem; border-radius:.85rem; font-size:.65rem; text-align:center; backdrop-filter:blur(4px); -webkit-backdrop-filter:blur(4px); }
.vital span { display:block; opacity:.6; font-weight:500; }
.vital strong { font-size:.95rem; }
.pulse-chart { position:absolute; inset:0; background: repeating-linear-gradient(90deg, rgba(255,255,255,.05) 0 8px, transparent 8px 16px); animation: scroll-grid 6s linear infinite; }
@keyframes scroll-grid { to { transform:translateX(-50%); } }

/* Buttons */
.btn { --btn-bg:var(--color-surface-alt); --btn-color:var(--color-text); position:relative; border:1px solid var(--color-border); background:var(--btn-bg); color:var(--btn-color); font-weight:600; font-size:.9rem; letter-spacing:.3px; padding:.75rem 1.2rem; border-radius:var(--radius-pill); cursor:pointer; text-decoration:none; display:inline-flex; align-items:center; gap:.5rem; line-height:1.1; transition:var(--trans-base); box-shadow:var(--shadow-sm); }
.btn:hover, .btn:focus-visible { box-shadow:var(--shadow-md); transform:translateY(-2px); }
.btn:active { transform:translateY(0); }
.btn--primary { --btn-bg:var(--gradient-primary); --btn-color:#fff; border:0; }
.btn--primary::after { content:""; position:absolute; inset:0; border-radius:inherit; background:linear-gradient(90deg,#fff3,transparent,#fff3); opacity:0; transition:var(--trans-fast); }
.btn--primary:hover::after { opacity:.4; }
.btn--primary-solid { border:0; background:var(--color-primary); color:#fff; font-weight:600; }
.btn--primary-solid:hover { filter:brightness(.95); }
.btn--primary-solid:focus-visible { outline:3px solid rgba(255,255,255,.55); outline-offset:2px; }
/* No dark override needed; follows CSS variables */
/* Keep nav version text forced white (in case of inheritance from nav link styling) */
.nav__menu a.btn--primary-solid { color:#fff; }
.btn--center { justify-content:center; margin-left:auto; margin-right:auto; display:inline-flex; }
.btn--outline { background:transparent; border:1px solid var(--color-border); }
.btn--ghost { background:transparent; border:0; box-shadow:none; }
.btn--lg { font-size:1rem; padding:1rem 1.55rem; }

/* Feature grid */
.features__grid { display:grid; gap:1.5rem; max-width:var(--container-max); margin:0 auto; grid-template-columns:repeat(auto-fit,minmax(240px,1fr)); }
.feature-card { background:var(--color-surface); border:1px solid var(--color-border); padding:1.25rem 1.15rem 1.4rem; border-radius:1.3rem; box-shadow:var(--shadow-sm); position:relative; overflow:hidden; display:flex; flex-direction:column; gap:.6rem; transition:var(--trans-base), transform .6s cubic-bezier(.19,1,.22,1); isolation:isolate; }
.feature-card::before { content:""; position:absolute; inset:0; background:linear-gradient(135deg,rgba(0,180,216,.08),rgba(56,176,0,.06)); opacity:0; transition:var(--trans-base); z-index:-1; }
.feature-card__icon { font-size:1.8rem; filter:drop-shadow(0 4px 6px rgba(0,0,0,.15)); }
.feature-card h3 { margin:.25rem 0 .35rem; font-size:1.05rem; }
.feature-card p { margin:0; font-size:.85rem; line-height:1.4; color:var(--color-text-soft); }
.feature-card:hover, .feature-card:focus-visible { transform:translateY(-6px) scale(1.015); box-shadow:var(--shadow-lg); }
.feature-card:hover::before, .feature-card:focus-visible::before { opacity:1; }
body.dark .feature-card { background:var(--color-surface-alt); }

/* Dashboard preview */
.dashboard__inner { display:grid; gap:3rem; max-width:var(--container-max); margin:0 auto; align-items:center; }
@media (min-width: 1000px) { .dashboard__inner { grid-template-columns: repeat(2,1fr); } }
.dashboard__text .lead { font-size:1.05rem; color:var(--color-text-soft); }
.check-list { list-style:none; margin:1.25rem 0 0; padding:0; display:grid; gap:.6rem; }
.check-list li { padding-left:1.4rem; position:relative; font-size:.9rem; }
.check-list li::before { content:""; position:absolute; left:0; top:.35rem; width:.85rem; height:.85rem; background:linear-gradient(90deg,#00b4d8,#38b000); border-radius:50%; box-shadow:0 0 0 2px var(--color-surface); }
.dashboard-frame { position:relative; aspect-ratio:16/10; background:linear-gradient(145deg,#06141f,#0d2533); border-radius:1.6rem; overflow:hidden; box-shadow:var(--shadow-lg); isolation:isolate; }
.dashboard-frame .layer { position:absolute; inset:0; /* Removed animated striped background */ background:none; animation:none; }
/* Removed colored gradient that appeared to creep; keep transparent so ECG stands alone */
.dashboard-frame .layer--back { background:none; }
.dashboard-frame .layer--back #ecg-canvas { position:absolute; inset:0; width:100%; height:100%; display:block; mix-blend-mode:normal; opacity:1; }
body.dark .dashboard-frame .layer--back #ecg-canvas { mix-blend-mode:normal; opacity:1; }
/* Neutralize extra blur / blend to eliminate color wash */
.dashboard-frame .layer--mid, .dashboard-frame .layer--front { filter:none; mix-blend-mode:normal; opacity:1; animation:none; }
/* Subtle themed tints on top layer: green in light, blue in dark */
.dashboard-frame .layer--front {
    background:
        radial-gradient(120px 80px at 70% 30%, rgba(34,197,94,.15), transparent 60%),
        radial-gradient(80px 60px at 30% 70%, rgba(34,197,94,.12), transparent 60%);
}
body.dark .dashboard-frame .layer--front {
    background:
        radial-gradient(120px 80px at 70% 30%, rgba(59,130,246,.18), transparent 60%),
        radial-gradient(80px 60px at 30% 70%, rgba(59,130,246,.14), transparent 60%);
}
@keyframes move-bg { to { transform:translateX(-50%); } }
.mini-card { position:absolute; background:linear-gradient(135deg,var(--color-primary-accent),var(--color-accent)); color:#fff; font-size:.65rem; padding:.55rem .7rem; border-radius:.9rem; box-shadow:var(--shadow-md); animation: float 6s ease-in-out infinite; }
.mini-card.trend-card { top:14%; left:10%; }
.mini-card.alert-card { bottom:16%; right:12%; background:linear-gradient(135deg,#e63946,#ff7b89); animation-duration:5s; }
@keyframes float { 0%,100% { transform:translateY(-6px);} 50% { transform:translateY(6px);} }
.spark { width:100%; height:.35rem; margin-top:.4rem; background:linear-gradient(90deg,rgba(255,255,255,.3),#fff,rgba(255,255,255,.3)); border-radius:4px; position:relative; overflow:hidden; }
.spark::before { content:""; position:absolute; inset:0; background:linear-gradient(90deg,transparent,rgba(255,255,255,.9),transparent); animation: shimmer 2s linear infinite; }
@keyframes shimmer { to { transform:translateX(100%); } }

/* Carousel */
.carousel { position:relative; max-width:900px; margin:0 auto; overflow:hidden; }
.carousel__track { display:flex; transition: transform .7s var(--trans-base); will-change:transform; }
.testimonial { flex:0 0 100%; padding:1.25rem 1rem 2.25rem; margin:0; position:relative; }
@media (min-width: 700px){ .testimonial { padding:2rem 3rem 3.25rem; } }
.testimonial blockquote { font-size:clamp(1rem,1.6vw,1.25rem); font-weight:500; line-height:1.5; margin:0 0 1.25rem; position:relative; }
.testimonial blockquote::before { content:"“"; position:absolute; left:-1.25rem; top:-.75rem; font-size:3rem; opacity:.15; line-height:1; }
.testimonial figcaption { font-size:.85rem; font-weight:600; letter-spacing:.5px; text-transform:uppercase; color:var(--color-text-soft); }
.carousel__controls { position:absolute; inset:0; display:flex; align-items:center; justify-content:space-between; pointer-events:none; }
.carousel__btn { pointer-events:auto; background:var(--color-surface); border:1px solid var(--color-border); width:2.4rem; height:2.4rem; display:grid; place-items:center; border-radius:var(--radius-pill); cursor:pointer; font-size:1.3rem; font-weight:600; transition:var(--trans-base); }
.carousel__btn:hover { background:var(--color-primary); color:#fff; }
.carousel__dots { display:flex; justify-content:center; gap:.6rem; margin-top:1rem; }
.carousel__dots button { width:.75rem; height:.75rem; border-radius:50%; border:1px solid var(--color-border); background:var(--color-surface-alt); cursor:pointer; position:relative; overflow:hidden; transition:var(--trans-base); }
.carousel__dots button[aria-selected="true"] { background:var(--color-primary); border-color:var(--color-primary); transform:scale(1.25); }

/* CTA */
.cta { background:var(--gradient-primary); color:#fff; position:relative; overflow:hidden; }
.cta::before { content:""; position:absolute; inset:0; background:radial-gradient(circle at 35% 40%,rgba(255,255,255,.35),transparent 60%), radial-gradient(circle at 65% 60%,rgba(255,255,255,.25),transparent 55%); mix-blend-mode:overlay; }
.cta__inner { max-width:760px; margin:0 auto; text-align:center; position:relative; }
.cta__title { font-size:clamp(1.9rem,4vw,2.75rem); margin:0 0 1rem; letter-spacing:-.5px; }
.cta__subtitle { margin:0 0 2rem; font-size:1.05rem; font-weight:500; }
.cta__form { display:flex; flex-direction:column; gap:.85rem; align-items:center; justify-content:center; max-width:520px; margin:0 auto 1.25rem; }
@media (min-width: 620px){ .cta__form { flex-direction:row; } }
.cta__form input { flex:1; width:100%; padding:1rem 1.1rem; border-radius:var(--radius-pill); border:0; font-size:.95rem; outline:none; box-shadow:var(--shadow-md); }
.cta__form input:focus-visible { outline:3px solid rgba(255,255,255,.6); }
.cta__note { font-size:.75rem; opacity:.85; }

/* Footer */
.site-footer { background:var(--color-bg-alt); padding:4rem 1.25rem 2rem; margin-top:4rem; font-size:.85rem; }
body.dark .site-footer { background:#0f151b; }
.footer__grid { max-width:var(--container-max); margin:0 auto 2rem; display:grid; gap:2.5rem; grid-template-columns:repeat(auto-fit,minmax(180px,1fr)); }
.footer__brand p { max-width:280px; font-size:.8rem; color:var(--color-text-soft); }
.footer__title { font-size:.85rem; text-transform:uppercase; letter-spacing:1px; font-weight:600; margin:0 0 .85rem; color:var(--color-text-soft); }
.footer__nav ul { list-style:none; margin:0; padding:0; display:grid; gap:.45rem; }
.footer__nav a { text-decoration:none; color:var(--color-text); font-size:.8rem; padding:.25rem 0; }
.footer__nav a:hover { color:var(--color-primary); }
.social-list { list-style:none; display:flex; gap:.75rem; margin:0; padding:0; }
.social { width:2.25rem; height:2.25rem; display:grid; place-items:center; background:var(--color-surface); border-radius:var(--radius-pill); font-size:1.05rem; text-decoration:none; border:1px solid var(--color-border); transition:var(--trans-base); }
.social:hover { background:var(--color-primary); border-color:var(--color-primary); color:#fff; }
.footer__bottom { border-top:1px solid var(--color-border); max-width:var(--container-max); margin:0 auto; padding:1.25rem 0 0; display:flex; flex-wrap:wrap; gap:1rem; align-items:center; justify-content:space-between; }
.back-to-top { background:var(--color-surface); border:1px solid var(--color-border); width:2.4rem; height:2.4rem; display:grid; place-items:center; border-radius:var(--radius-pill); cursor:pointer; font-size:1.1rem; transition:var(--trans-base); }
.back-to-top:hover { background:var(--color-primary); color:#fff; }

/* Canvas background */
#bg-canvas { position:fixed; inset:0; width:100%; height:100%; pointer-events:none; z-index:-1; background:radial-gradient(circle at 60% 30%,color-mix(in oklab, var(--color-primary-accent) 35%, transparent),transparent 60%), radial-gradient(circle at 20% 70%,color-mix(in oklab, var(--color-primary) 30%, transparent),transparent 55%); }
body.dark #bg-canvas { background:radial-gradient(circle at 60% 30%,color-mix(in oklab, var(--color-primary-accent) 45%, transparent),transparent 60%), radial-gradient(circle at 20% 70%,color-mix(in oklab, var(--color-primary) 35%, transparent),transparent 55%); }

/* Micro interactions */
[data-micro="bounce"]:hover { animation: micro-bounce .8s; }
@keyframes micro-bounce { 20% { transform:translateY(-4px);} 40% { transform:translateY(2px);} 60% { transform:translateY(-2px);} }
[data-micro="pulse"]:hover { animation: micro-pulse 1.2s infinite; }
@keyframes micro-pulse { 0%,100% { box-shadow:0 0 0 0 rgba(255,255,255,.5);} 50% { box-shadow:0 0 0 8px rgba(255,255,255,0);} }

/* Animations (scroll reveal) */
[data-animate] { opacity:0; transform:translateY(24px); transition: opacity .9s var(--trans-base), transform .9s var(--trans-base); }
[data-animate].is-visible { opacity:1; transform:none; }
[data-animate="fade-down"] { transform:translateY(-20px); }
[data-animate="zoom-in"] { transform:scale(.9); }
[data-animate="fade-left"] { transform:translateX(-40px); }
[data-animate="fade-right"] { transform:translateX(40px); }
[data-animate="slide-in"] { transform:translateX(60px); }

/* Utility */
.sr-only { position:absolute; width:1px; height:1px; padding:0; margin:-1px; overflow:hidden; clip:rect(0 0 0 0); white-space:nowrap; border:0; }
.no-bg { background:transparent !important; }
.no-border { border:0 !important; }
.no-shadow { box-shadow:none !important; }
.ml-auto { margin-left:auto !important; }
.text-center { text-align:center !important; }
.mt-8 { margin-top:2rem !important; }
.mt-6 { margin-top:1.5rem !important; }
.mt-0 { margin-top:0 !important; }
.text-sm { font-size:.85rem !important; }
.text-2xs { font-size:.65rem !important; }
.op-70 { opacity:.7 !important; }
.hidden { display:none !important; }
.grid { display:grid !important; }
.gap-1 { gap:1rem !important; }
.gap-075 { gap:.75rem !important; }
.sm-grid-cols-2 { grid-template-columns:repeat(auto-fit,minmax(180px,1fr)) !important; }
.link { color:var(--color-primary); text-decoration:none; }
.shimmer { position:relative; }
.shimmer::after { content:""; position:absolute; inset:0; background:linear-gradient(-75deg,transparent 0 40%,rgba(255,255,255,.3) 45% 55%,transparent 60% 100%); mix-blend-mode:overlay; animation: shimmer-move 5s linear infinite; }
@keyframes shimmer-move { to { background-position:200% 0; } }

/* Mobile navigation */
@media (max-width: 860px) {
    .nav__toggle { display:inline-block; }
    .nav__menu { position:fixed; inset:0 0 0 auto; width:min(260px,80%); background:var(--color-bg); flex-direction:column; padding:6rem 1.25rem 2rem; box-shadow:-4px 0 16px -4px rgba(0,0,0,.15); transform:translateX(100%); transition:var(--trans-base); }
    body.dark .nav__menu { background:#0d1116; }
    .nav__menu[data-open="true"] { transform:translateX(0); }
    .nav__menu a, .nav__menu button { width:100%; justify-content:flex-start; }
}

/* Dark mode adjustments */
body.dark .feature-card { border-color:#2c3a46; }
body.dark .device-mock { background:#0d141b; box-shadow: inset 0 0 0 2px #13202b, var(--shadow-lg); }
body.dark .btn--outline { border-color:#2c3a46; }
body.dark .carousel__btn { background:#18232d; }
body.dark .carousel__dots button { background:#1d2933; border-color:#2c3a46; }

/* Reduced motion critical overrides are above */

/* End styles */
/* Auth pages adaptive blurred background */
.auth-wrapper { position:relative; min-height:100dvh; display:flex; flex-direction:column; }
.auth-gradient { position:absolute; inset:0; pointer-events:none; overflow:hidden; }
.auth-gradient::before, .auth-gradient::after { content:""; position:absolute; width:780px; height:780px; top:50%; left:50%; transform:translate(-50%,-50%); filter:blur(140px); opacity:.55; border-radius:50%; }
.auth-gradient::before { background:radial-gradient(circle at 30% 35%, rgba(0,119,182,.55), transparent 65%), radial-gradient(circle at 70% 65%, rgba(56,176,0,.5), transparent 60%), linear-gradient(140deg, rgba(0,119,182,.35), rgba(0,180,216,.25), rgba(56,176,0,.25)); mix-blend-mode:screen; }
.auth-gradient::after { background:radial-gradient(circle at 60% 40%, rgba(0,180,216,.4), transparent 60%), radial-gradient(circle at 40% 70%, rgba(0,119,182,.35), transparent 65%); mix-blend-mode:overlay; }
body.dark .auth-gradient::before { background:radial-gradient(circle at 30% 35%, rgba(56,176,0,.45), transparent 65%), radial-gradient(circle at 70% 65%, rgba(0,180,216,.5), transparent 60%), linear-gradient(140deg, rgba(0,119,182,.25), rgba(0,180,216,.2), rgba(56,176,0,.35)); mix-blend-mode:screen; }
body.dark .auth-gradient::after { background:radial-gradient(circle at 55% 45%, rgba(0,180,216,.35), transparent 62%), radial-gradient(circle at 40% 70%, rgba(56,176,0,.4), transparent 65%); }
@media (max-width: 640px){ .auth-gradient::before, .auth-gradient::after { width:520px; height:520px; filter:blur(110px); opacity:.55; } }
.auth-main { position:relative; z-index:1; flex:1; display:flex; align-items:center; }
.auth-card { backdrop-filter:blur(22px); -webkit-backdrop-filter:blur(22px); background:rgba(255,255,255,.78); border:1px solid rgba(255,255,255,.6); }
body.dark .auth-card { background:rgba(13,17,22,.72); border-color:rgba(255,255,255,.07); }
/* Auth forms layout */
.form-control { padding:.85rem 1rem; border:1px solid var(--color-border); border-radius:var(--radius-md); background:var(--color-surface-alt); font:inherit; color:inherit; }
.form-control:focus-visible { outline:2px solid var(--color-primary); outline-offset:2px; }
body.dark .form-control { background:var(--color-surface-alt); border-color:#2c3a46; }
.role-switch .role-option input { accent-color:var(--color-primary); }
.role-switch .role-option { transition:var(--trans-fast), border-color var(--trans-fast); }
.role-switch { display:flex; flex-wrap:wrap; gap:.75rem; justify-content:center; }
.role-switch .role-option:has(input:checked) { background:var(--gradient-primary); color:#fff; border-color:transparent; box-shadow:var(--shadow-sm); }
body.dark .role-switch .role-option:has(input:checked) { background:linear-gradient(135deg,#00b4d8,#38b000); }
.role-fields { animation: fade-role .35s var(--trans-fast); }
@keyframes fade-role { from { opacity:0; transform:translateY(4px);} to { opacity:1; transform:none;} }
/* Onboarding */
.onb-cards .onb-card { background:var(--color-surface); border:1px solid var(--color-border); padding:1.1rem 1rem 1.2rem; border-radius:1.1rem; box-shadow:var(--shadow-sm); display:flex; flex-direction:column; gap:.55rem; position:relative; overflow:hidden; }
body.dark .onb-cards .onb-card { background:var(--color-surface-alt); }
.onb-cards .onb-card h3 { margin:.2rem 0 .1rem; font-size:1rem; }
.onb-cards .onb-card p { margin:0; font-size:.75rem; color:var(--color-text-soft); }
.onb-cards .onb-card.completed { border-color:var(--color-accent); }
.onb-cards .onb-card.completed::after { content:"✓"; position:absolute; top:.6rem; right:.7rem; background:var(--color-accent); color:#fff; width:1.25rem; height:1.25rem; display:grid; place-items:center; font-size:.75rem; border-radius:50%; box-shadow:0 0 0 2px var(--color-surface); }
.onb-cards .onb-card .small { font-size:.7rem; padding:.55rem .9rem; }

.progress-wrapper { background:var(--color-surface-alt); border:1px solid var(--color-border); height:.65rem; border-radius:var(--radius-pill); position:relative; overflow:hidden; }
.progress-bar { --p:0%; position:absolute; inset:0; width:var(--p); background:linear-gradient(90deg,var(--color-accent),var(--color-primary)); transition:width .5s var(--trans-base); }
body.dark .progress-bar { background:linear-gradient(90deg,var(--color-accent),var(--color-primary)); }

/* Modal */
.modal { position:fixed; inset:0; display:grid; place-items:center; z-index:200; }
.modal[hidden] { display:none !important; }
.modal__backdrop { position:absolute; inset:0; background:rgba(0,0,0,.45); backdrop-filter:blur(6px); -webkit-backdrop-filter:blur(6px); }
.modal__dialog { position:relative; width:min(540px,92%); background:var(--color-surface); border:1px solid var(--color-border); border-radius:1.3rem; padding:1.75rem 1.65rem 1.9rem; box-shadow:var(--shadow-lg); display:flex; flex-direction:column; gap:1rem; animation: modal-in .5s cubic-bezier(.4,0,.2,1); }
body.dark .modal__dialog { background:var(--color-surface-alt); }
.modal__close { position:absolute; top:.65rem; right:.65rem; background:var(--color-surface-alt); border:1px solid var(--color-border); width:2rem; height:2rem; border-radius:var(--radius-pill); cursor:pointer; font-size:1rem; display:grid; place-items:center; }
.modal__actions { margin-top:.5rem; display:flex; flex-wrap:wrap; gap:.75rem; justify-content:flex-end; }
@keyframes modal-in { from { opacity:0; transform:translateY(16px) scale(.96);} to { opacity:1; transform:none;} }

.onb-form .form-row { display:flex; flex-direction:column; gap:.4rem; margin-bottom:.85rem; font-size:.75rem; }
.onb-form .form-row.two { flex-direction:row; gap:.85rem; }
.onb-form .form-row.two > label { flex:1; }
.onb-form input, .onb-form select, .onb-form textarea { width:100%; padding:.7rem .85rem; border:1px solid var(--color-border); background:var(--color-surface-alt); border-radius:.7rem; font:inherit; color:inherit; }
.onb-form input:focus-visible, .onb-form select:focus-visible, .onb-form textarea:focus-visible { outline:2px solid var(--color-primary); outline-offset:2px; }
body.dark .onb-form input, body.dark .onb-form select, body.dark .onb-form textarea { background:var(--color-surface); border-color:#2c3a46; }

/* Global form labels and controls */
label { font-size:.85rem; font-weight:600; color:var(--color-text-soft); }
label > span { display:block; font-size:.8rem; font-weight:600; color:var(--color-text-soft); }
input[type="text"], input[type="email"], input[type="number"], input[type="date"], input[type="file"], select, textarea {
    width:100%; max-width:100%; box-sizing:border-box; padding:.7rem .85rem; border:1px solid var(--color-border); border-radius:.65rem; background:var(--color-surface); color:inherit; font:inherit;
}
textarea { min-height:96px; resize:vertical; }
input:focus-visible, select:focus-visible, textarea:focus-visible { outline:2px solid var(--color-primary); outline-offset:2px; }
body.dark input, body.dark select, body.dark textarea { background:var(--color-surface-alt); border-color:#2c3a46; }
/* Light mode visibility adjustments for auth gradient */
body:not(.dark) .auth-gradient { background:radial-gradient(circle at 65% 35%, rgba(0,180,216,.18), transparent 60%), radial-gradient(circle at 30% 70%, rgba(56,176,0,.18), transparent 60%); }
body:not(.dark) .auth-gradient::before { mix-blend-mode:normal; opacity:.75; filter:blur(130px) brightness(1.05); }
body:not(.dark) .auth-gradient::after { mix-blend-mode:normal; opacity:.5; filter:blur(150px) saturate(115%); }
/* Slight boost for users with prefers-reduced-motion still keeps static glow */
@media (prefers-reduced-motion: reduce){ body:not(.dark) .auth-gradient::before, body:not(.dark) .auth-gradient::after { filter:blur(120px); } }

/* Onboarding flow specific styles */
.onb-container { max-width:980px; margin:0 auto; display:grid; gap:1rem; }
.onb-progress { position:sticky; top:70px; background:var(--color-surface); border:1px solid var(--color-border); border-radius:var(--radius-pill); padding:.35rem; box-shadow:var(--shadow-sm); display:flex; align-items:center; gap:.75rem; }
.onb-progress__bar { position:absolute; height:.35rem; left:.5rem; right:.5rem; top:-.85rem; background:linear-gradient(90deg,var(--color-accent),var(--color-primary)); border-radius:999px; box-shadow:0 2px 8px -2px color-mix(in oklab, var(--color-primary) 40%, transparent); }
.onb-steps { list-style:none; display:flex; gap:.5rem; margin:.1rem 0 0 auto; padding:0; }
.onb-step { width:1.9rem; height:1.9rem; display:grid; place-items:center; border-radius:50%; background:var(--color-surface-alt); border:1px solid var(--color-border); color:var(--color-text-soft); font-weight:700; font-size:.9rem; transition:var(--trans-base); position:relative; }
.onb-step--active { background:var(--color-primary); border-color:transparent; color:#fff; box-shadow:0 6px 16px -6px color-mix(in oklab, var(--color-primary) 55%, transparent); }
.onb-step--done { color:transparent; }
.onb-step--done::after { content:"✓"; position:absolute; inset:0; display:grid; place-items:center; color:#fff; background:var(--color-primary); border-radius:50%; }

.onb-step-pane { display:none; animation: onb-fade .45s var(--trans-base); }
.onb-step-pane--active { display:block; }
@keyframes onb-fade { from { opacity:0; transform: translateY(12px);} to { opacity:1; transform:none;} }

.onb-actions { display:flex; gap:.75rem; justify-content:flex-end; margin-top:1rem; }
.onb-error { background: #fee2e2; color:#7f1d1d; padding:.75rem 1rem; border-radius:.8rem; border:1px solid #fecaca; margin-bottom:1rem; }

.icon-btn { background:transparent; border:1px solid var(--color-border); width:2.25rem; height:2.25rem; border-radius:50%; display:grid; place-items:center; cursor:pointer; }
.icon-btn:hover { background:var(--color-surface-alt); }
.avatar-menu { position:relative; }
.avatar-menu .avatar { background:transparent; border:0; padding:0; border-radius:999px; overflow:hidden; width:2.2rem; height:2.2rem; }
.avatar-menu img { width:100%; height:100%; display:block; }
.avatar-menu .menu { position:absolute; right:0; top:calc(100% + .45rem); background:var(--color-surface); border:1px solid var(--color-border); border-radius:.8rem; padding:.5rem; list-style:none; box-shadow:var(--shadow-lg); display:none; min-width:180px; z-index:60; }
.avatar-menu .menu a, .avatar-menu .menu .link-btn { display:block; width:100%; text-decoration:none; padding:.5rem .6rem; border-radius:.5rem; color:var(--color-text); background:transparent; border:0; text-align:left; }
.avatar-menu .menu a:hover, .avatar-menu .menu .link-btn:hover { background:var(--color-surface-alt); }
/* Fallback: show menu when button is expanded without relying on :has() */
.avatar-menu .avatar[aria-expanded="true"] + .menu { display:block; }
/* Progressive enhancement (supported in modern Chromium/WebKit) */
.avatar-menu:has(.avatar[aria-expanded="true"]) .menu { display:block; }

.confetti { position:fixed; inset:0; pointer-events:none; z-index:100; opacity:0; transition: opacity .4s ease; }
.confetti--show { opacity:1; }
