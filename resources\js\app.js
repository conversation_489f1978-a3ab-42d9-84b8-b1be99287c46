/* Healthcare Tracker Landing Page Interactions */

// Utility: throttle
function throttle(fn, wait = 100) { let last = 0; return (...args) => { const now = Date.now(); if (now - last >= wait) { last = now; fn(...args); } }; }

// Use local bundled NextParticle version (UMD) to avoid CDN blocking.
import './nextparticle.min.js';
import { initHeartParticle } from './heart.js';
import { initHeartbeat, destroyHeartbeat } from './heartbeat.js';

document.addEventListener('DOMContentLoaded', () => {
	const root = document.documentElement;
	const body = document.body;

	// Mobile nav toggle
	const navToggle = document.getElementById('navToggle');
	const navMenu = document.getElementById('navMenu');
	if (navToggle && navMenu) {
		navToggle.addEventListener('click', () => {
			const expanded = navToggle.getAttribute('aria-expanded') === 'true';
			navToggle.setAttribute('aria-expanded', String(!expanded));
			navMenu.dataset.open = String(!expanded);
			if (!expanded) {
				navMenu.querySelector('a,button')?.focus({ preventScroll: true });
			} else {
				navToggle.focus({ preventScroll: true });
			}
		});
		// Close on ESC
		document.addEventListener('keydown', e => {
			if (e.key === 'Escape' && navMenu.dataset.open === 'true') {
				navToggle.click();
			}
		});
	}

	// Dark mode toggle (persist in localStorage)
	const themeToggle = document.getElementById('themeToggle');
	const THEME_KEY = 'ht-theme';
	function applyTheme(mode) {
		if (mode === 'dark') body.classList.add('dark'); else body.classList.remove('dark');
		if (themeToggle) {
			themeToggle.setAttribute('aria-pressed', mode === 'dark');
			themeToggle.textContent = mode === 'dark' ? '☀️' : '🌙';
		}
		// Notify listeners (e.g. heart particle) of theme change
		document.dispatchEvent(new CustomEvent('theme-changed', { detail: { mode } }));
	}
	const preferred = localStorage.getItem(THEME_KEY) || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark':'light');
	applyTheme(preferred);
	themeToggle?.addEventListener('click', () => {
		const isDark = body.classList.toggle('dark');
		const mode = isDark ? 'dark' : 'light';
		localStorage.setItem(THEME_KEY, mode);
		applyTheme(mode);
	});

	// Scroll reveal animations
	const animated = [...document.querySelectorAll('[data-animate]')];
	const observer = new IntersectionObserver((entries) => {
		entries.forEach(entry => {
			if (entry.isIntersecting) {
				const el = entry.target;
				const delay = el.getAttribute('data-delay');
				if (delay) el.style.transitionDelay = delay + 'ms';
				el.classList.add('is-visible');
				observer.unobserve(el);
			}
		});
	}, { threshold: 0.15, rootMargin: '0px 0px -40px 0px' });
	animated.forEach(el => observer.observe(el));

	// Stats counter
	const counters = document.querySelectorAll('.stat__num[data-count]');
	counters.forEach(el => {
		const target = parseInt(el.getAttribute('data-count') || '0', 10);
		let current = 0; const duration = 1600; const start = performance.now();
		function update(ts) {
			const p = Math.min(1, (ts - start)/duration); current = Math.floor(p * target);
			el.textContent = current.toLocaleString();
			if (p < 1) requestAnimationFrame(update); else el.textContent = target.toLocaleString();
		}
		requestAnimationFrame(update);
	});

	// Testimonials carousel
	const track = document.getElementById('testimonialTrack');
	const prevBtn = document.getElementById('prevTestimonial');
	const nextBtn = document.getElementById('nextTestimonial');
	const dotsContainer = document.getElementById('testimonialDots');
	if (track && prevBtn && nextBtn && dotsContainer) {
		const slides = Array.from(track.children);
		let index = 0; let autoTimer;
		function renderDots() {
			dotsContainer.innerHTML = '';
			slides.forEach((_, i) => {
				const b = document.createElement('button');
				b.setAttribute('role','tab');
				b.setAttribute('aria-selected', String(i===index));
				b.addEventListener('click', () => { index = i; update(); restartAuto(); });
				dotsContainer.appendChild(b);
			});
		}
		function update() {
			track.style.transform = `translateX(-${index * 100}%)`;
			dotsContainer.querySelectorAll('button').forEach((b,i)=>b.setAttribute('aria-selected', String(i===index)));
		}
		function next(){ index = (index + 1) % slides.length; update(); }
		function prev(){ index = (index - 1 + slides.length) % slides.length; update(); }
		function startAuto() { autoTimer = setInterval(next, 6000); }
		function restartAuto(){ clearInterval(autoTimer); startAuto(); }
		nextBtn.addEventListener('click', ()=>{ next(); restartAuto(); });
		prevBtn.addEventListener('click', ()=>{ prev(); restartAuto(); });
		renderDots(); update(); startAuto();
		track.addEventListener('pointerdown', e => { const startX = e.clientX; let diff=0; function move(ev){ diff = ev.clientX - startX; }
			function up(){ if (Math.abs(diff) > 50){ if (diff < 0) next(); else prev(); restartAuto(); } window.removeEventListener('pointermove', move); window.removeEventListener('pointerup', up); }
			window.addEventListener('pointermove', move); window.addEventListener('pointerup', up); });
	}

	// Parallax effect for dashboard
	const parallax = document.querySelector('.dashboard-frame');
	if (parallax) {
		parallax.addEventListener('mousemove', throttle(e => {
			const r = parallax.getBoundingClientRect();
			const x = (e.clientX - r.left)/r.width - .5;
			const y = (e.clientY - r.top)/r.height - .5;
			parallax.querySelectorAll('.layer').forEach((layer,i)=>{
				const depth = (i+1) * 8;
				layer.style.transform = `translate(${x*depth}px, ${y*depth}px)`;
			});
		}, 40));
		parallax.addEventListener('mouseleave', () => {
			parallax.querySelectorAll('.layer').forEach(layer=>layer.style.transform='');
		});
	}

	// Tilt effect for hero mock
	const tiltEl = document.querySelector('[data-tilt]');
	if (tiltEl) {
		tiltEl.addEventListener('mousemove', throttle(e => {
			const r = tiltEl.getBoundingClientRect();
			const x = (e.clientX - r.left)/r.width - .5;
			const y = (e.clientY - r.top)/r.height - .5;
			tiltEl.style.transform = `rotateX(${(-y*10).toFixed(2)}deg) rotateY(${(x*12).toFixed(2)}deg) translateY(-4px)`;
		}, 32));
		tiltEl.addEventListener('mouseleave', () => tiltEl.style.transform='');
	}

	// Back to top
	const backToTop = document.getElementById('backToTop');
	if (backToTop) {
		backToTop.addEventListener('click', () => { window.scrollTo({ top:0, behavior:'smooth' }); });
		window.addEventListener('scroll', throttle(()=>{
			backToTop.style.opacity = window.scrollY > 800 ? '1':'0';
			backToTop.style.pointerEvents = window.scrollY > 800 ? 'auto':'none';
		}, 150));
	}

	// Avatar dropdown (global)
	document.addEventListener('click', (e) => {
		const menuRoot = e.target.closest('.avatar-menu');
		const avatarBtn = e.target.closest('.avatar');
		// Close all if click outside menus
		if (!menuRoot) {
			document.querySelectorAll('.avatar-menu .avatar[aria-expanded="true"]').forEach(btn => btn.setAttribute('aria-expanded','false'));
			return;
		}
		// Toggle the clicked one
		if (avatarBtn) {
			const expanded = avatarBtn.getAttribute('aria-expanded') === 'true';
			// Close others first
			document.querySelectorAll('.avatar-menu .avatar').forEach(btn => { if (btn !== avatarBtn) btn.setAttribute('aria-expanded','false'); });
			avatarBtn.setAttribute('aria-expanded', expanded ? 'false' : 'true');
		}
	});
	// Close on Escape
	document.addEventListener('keydown', (e) => {
		if (e.key === 'Escape') {
			document.querySelectorAll('.avatar-menu .avatar[aria-expanded="true"]').forEach(btn => btn.setAttribute('aria-expanded','false'));
		}
	});

	// Dynamic year
	const yearEl = document.getElementById('year');
	if (yearEl) yearEl.textContent = new Date().getFullYear();

	// Register form role-based dynamic fields (no backend logic yet)
	const registerForm = document.getElementById('registerForm');
	if (registerForm) {
		const roleRadios = registerForm.querySelectorAll('input[name="role"]');
		const patientGroup = document.getElementById('patientFields');
		const doctorGroup = document.getElementById('doctorFields');
		function setRequired(group, enabled) {
			if (!group) return; group.querySelectorAll('input,select,textarea').forEach(el => {
				if (enabled) {
					if (el.dataset.origRequired === 'true') el.required = true;
				} else {
					if (el.required) { el.dataset.origRequired = 'true'; }
					el.required = false;
				}
			});
		}
		function updateRole(role) {
			if (role === 'doctor') {
				patientGroup.style.display = 'none';
				doctorGroup.style.display = 'grid';
				setRequired(patientGroup,false); setRequired(doctorGroup,true);
			} else {
				doctorGroup.style.display = 'none';
				patientGroup.style.display = 'grid';
				setRequired(doctorGroup,false); setRequired(patientGroup,true);
			}
		}
		roleRadios.forEach(r => r.addEventListener('change', e => updateRole(e.target.value)));
		// initial
		updateRole(registerForm.querySelector('input[name="role"]:checked')?.value || 'patient');
	}

	// Onboarding logic (dashboard)
	const onboardingStateKey = 'ht-onboarding-completed-steps';
	const welcomeModal = document.getElementById('welcomeModal');
	const onbProgressBar = document.getElementById('onbProgress');
	const progressText = document.getElementById('progressText');
	const onbCards = document.getElementById('onbCards');
	if (onbCards) {
		let completed = new Set(JSON.parse(localStorage.getItem(onboardingStateKey) || '[]'));
		const total = 4; // profile, vitals, medical, notifications
		function pct(){ return Math.round((completed.size / total) * 100); }
		function updateUI(){
			if (onbProgressBar) onbProgressBar.style.setProperty('--p', pct() + '%');
			if (progressText) progressText.textContent = `You're ${pct()}% ready`;
			onbCards.querySelectorAll('.onb-card').forEach(card => {
				const step = card.getAttribute('data-step');
				card.classList.toggle('completed', completed.has(step));
			});
			if (completed.size === total && welcomeModal) welcomeModal.hidden = true;
		}
		updateUI();

		function store(){ localStorage.setItem(onboardingStateKey, JSON.stringify(Array.from(completed))); }

		// Modal management
		function openModal(id){
			const m = document.getElementById(id);
			if (!m) return; m.hidden = false; trapFocus(m);
		}
		function closeModal(modal){ modal.hidden = true; releaseFocus(); }
		document.querySelectorAll('.onb-open').forEach(btn => {
			btn.addEventListener('click', ()=>openModal(btn.getAttribute('data-target')));
		});
		document.querySelectorAll('.modal [data-close]').forEach(el => {
			el.addEventListener('click', ()=>closeModal(el.closest('.modal')));
		});
		document.addEventListener('keydown', e => { if (e.key === 'Escape') { document.querySelectorAll('.modal:not([hidden])').forEach(m=>closeModal(m)); } });

		// Completion buttons
		document.querySelectorAll('[data-complete]').forEach(btn => {
			btn.addEventListener('click', ()=>{
				const step = btn.getAttribute('data-complete');
				if (step){ completed.add(step); store(); updateUI(); closeModal(btn.closest('.modal')); }
			});
		});

		// Welcome modal logic (show only if not 100%)
		if (welcomeModal && completed.size < total) {
			setTimeout(()=>{ welcomeModal.hidden = false; trapFocus(welcomeModal); }, 450);
			const startBtn = document.getElementById('startOnboarding');
			startBtn?.addEventListener('click', ()=>{ closeModal(welcomeModal); openModal('modalProfile'); });
		}

		// Accessible focus trap
		let lastFocused = null; let activeTrap = null;
		function trapFocus(container){
			lastFocused = document.activeElement;
			activeTrap = container;
			const focusable = container.querySelectorAll('button,[href],input,select,textarea,[tabindex]:not([tabindex="-1"])');
			const first = focusable[0]; const last = focusable[focusable.length-1];
			first?.focus();
			function handler(e){
				if (e.key !== 'Tab') return;
				if (e.shiftKey && document.activeElement === first){ e.preventDefault(); last.focus(); }
				else if (!e.shiftKey && document.activeElement === last){ e.preventDefault(); first.focus(); }
			}
			container.addEventListener('keydown', handler);
			container.__trap = handler;
		}
		function releaseFocus(){ if (!activeTrap) return; activeTrap.removeEventListener('keydown', activeTrap.__trap); lastFocused?.focus(); activeTrap = null; }
	}

	// Animated canvas background (particles)
	const canvas = document.getElementById('bg-canvas');
	if (canvas) {
		const ctx = canvas.getContext('2d');
		let w, h, particles = [];
		function resize(){ w = canvas.width = window.innerWidth; h = canvas.height = window.innerHeight; }
		window.addEventListener('resize', throttle(resize, 250)); resize();
		function createParticles(){ particles = Array.from({length: 50}, ()=>({ x: Math.random()*w, y: Math.random()*h, r: Math.random()*2+1, dx: (Math.random()-.5)*0.4, dy: (Math.random()-.5)*0.4 })); }
		createParticles();
		function draw(){ ctx.clearRect(0,0,w,h); ctx.save(); ctx.globalAlpha = .65; particles.forEach(p=>{ ctx.beginPath(); ctx.fillStyle = `rgba(${p.r>2?0:0},${p.r>2?180:180},${p.r>2?216:176},${0.18 + p.r/14})`; ctx.arc(p.x,p.y,p.r,0,Math.PI*2); ctx.fill(); p.x+=p.dx; p.y+=p.dy; if (p.x<0||p.x>w) p.dx*=-1; if (p.y<0||p.y>h) p.dy*=-1; }); ctx.restore(); requestAnimationFrame(draw); }
		draw();
	}

	// Heart particle is initialized via initial 'theme-changed' dispatch inside applyTheme.
	// Removed direct init call to prevent double initialization (which created duplicate canvases).

	// Initialize ECG heartbeat visualization
	initHeartbeat();
	// Optional: re-init on window focus if needed after tab suspension
	window.addEventListener('visibilitychange', () => {
		if (document.visibilityState !== 'visible') return;
		const ecgEl = document.getElementById('ecg-canvas');
		if (!ecgEl) return; // not present on this page (e.g., /register)
		// simple heuristic: re-init on visibility restored
		destroyHeartbeat();
		initHeartbeat();
	});
});

import './bootstrap';
