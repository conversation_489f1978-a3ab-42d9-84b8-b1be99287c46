// Modular Heart Particle Initialization
// Strategy:
// 1. Generate an offscreen canvas heart shape (vector -> raster) to ensure guaranteed decode.
// 2. Convert to data URL, assign to Image, wait for onload.
// 3. Instantiate NextParticle with deterministic options.
// 4. Provide graceful fallback + resize handling.
// 5. Expose initHeart() that can be called after DOMContentLoaded.

function drawHeartToCanvas(size = 400, color = '#ff254a') {
  const cv = document.createElement('canvas');
  cv.width = size; cv.height = size;
  const ctx = cv.getContext('2d');
  ctx.fillStyle = color;
  ctx.beginPath();
  // Parametric heart path scaled into canvas
  // Using classic heart equation approximation with Bezier curving
  const w = size; const h = size;
  ctx.moveTo(w/2, h*0.9);
  ctx.bezierCurveTo(w*1.1, h*0.6, w*0.85, h*0.15, w/2, h*0.35);
  ctx.bezierCurveTo(w*0.15, h*0.15, -w*0.1, h*0.6, w/2, h*0.9);
  ctx.closePath();
  ctx.fill();
  return cv;
}

function createImageFromCanvas(cv) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = cv.toDataURL('image/png');
  });
}

function mountPlaceholder() {
  const container = document.querySelector('.heart-visual');
  if (!container) return null;
  let placeholder = container.querySelector('#heart-placeholder');
  if (!placeholder) {
    placeholder = document.createElement('div');
    placeholder.id = 'heart-placeholder';
    placeholder.style.position='relative';
    placeholder.style.width='100%';
    placeholder.style.height='100%';
    placeholder.style.display='flex';
    placeholder.style.alignItems='center';
    placeholder.style.justifyContent='center';
    placeholder.innerHTML = '<div class="heart-spinner" aria-hidden="true" style="width:48px;height:48px;border:4px solid #ff8aa0;border-top-color:transparent;border-radius:50%;animation:spin 0.9s linear infinite"></div>';
    container.appendChild(placeholder);
  }
  return placeholder;
}

function ensureStylesInjected() {
  if (document.getElementById('heart-inline-styles')) return;
  const style = document.createElement('style');
  style.id = 'heart-inline-styles';
  style.textContent = `@keyframes spin{to{transform:rotate(360deg)}}`;
  document.head.appendChild(style);
}

let currentHeartInstance = null;
let rebuilding = false;
let buildPromise = null; // prevent concurrent builds

function cleanupHeart(){
  const container = document.querySelector('.heart-visual');
  if(!container) return;
  if(currentHeartInstance){
    try { currentHeartInstance.stop(); } catch(_) {}
    currentHeartInstance = null;
  }
  // Remove any existing heart canvases (duplicate safeguard)
  container.querySelectorAll('canvas#heart-canvas').forEach(c=>c.remove());
  // Remove stray placeholders to ensure a fresh one is mounted later
  container.querySelectorAll('#heart-placeholder').forEach(p=>p.remove());
}

function getThemeHeartColor(){
  const dark = document.body.classList.contains('dark');
  // Reverse mapping: light = green, dark = blue
  return dark ? '#3b82f6' /* blue-500 */ : '#22c55e' /* green-500 */;
}

export async function initHeartParticle() {
  if (buildPromise) {
    console.log('[Heart] Build already in progress, skipping new init');
    return buildPromise;
  }
  buildPromise = (async () => {
    ensureStylesInjected();
    const container = document.querySelector('.heart-visual');
    if (!container) return;

    // Full cleanup to guarantee single instance
    cleanupHeart();
    mountPlaceholder();

    // Wait until container has non-zero layout (in case CSS / fonts not settled yet)
    const maxLayoutWait = 1000; // 1s
    let waitedLayout = 0;
    while (waitedLayout < maxLayoutWait) {
      const rect = container.getBoundingClientRect();
      if (rect.width > 10 && rect.height > 10) break; // minimal size threshold
      await new Promise(r => setTimeout(r, 50));
      waitedLayout += 50;
    }

    // If NextParticle not yet loaded, wait once using a Promise race
    await new Promise((resolve, reject) => {
      let waited = 0;
      function check() {
        if (window.NextParticle) return resolve();
        waited += 50;
        if (waited > 2000) return reject(new Error('NextParticle not available'));
        setTimeout(check, 50);
      }
      check();
    }).catch(err => { console.warn('[Heart] Library missing:', err.message); });

    if (!window.NextParticle) return; // abort gracefully

    // Generate procedural heart image
    let img;
    const heartColor = getThemeHeartColor();
    try {
      const heartCanvas = drawHeartToCanvas(480, heartColor);
      img = await createImageFromCanvas(heartCanvas);
    } catch (e) {
      console.warn('[Heart] Procedural heart generation failed, fallback to simple emoji', e);
      const emoji = document.createElement('div');
      emoji.textContent = '❤';
      emoji.style.fontSize = '180px';
      emoji.style.lineHeight = '1';
      const ph = document.getElementById('heart-placeholder');
      if (ph) { ph.innerHTML = ''; ph.appendChild(emoji); }
      return;
    }

    // Prepare explicit canvas to avoid undefined references inside library
    const explicitCanvas = document.createElement('canvas');
    explicitCanvas.id = 'heart-canvas';
    explicitCanvas.style.position = 'absolute';
    explicitCanvas.style.inset = '0';
    explicitCanvas.style.width = '100%';
    explicitCanvas.style.height = '100%';

    // Determine base size (fallback to 420 if container is still 0)
    const baseRect = container.getBoundingClientRect();
    const baseSize = Math.max(120, Math.min(baseRect.width || 420, baseRect.height || baseRect.width || 420));

    let np;
    try {
      np = new NextParticle({
        image: img,
        particleGap: 3,
        particleSize: 2.3,
        mouseForce: 35,
        gravity: 0.09,
        initPosition: 'random',
        initDirection: 'random',
        color: heartColor,
        width: baseSize,
        height: baseSize,
        maxWidth: 900,
        maxHeight: 900,
        canvas: explicitCanvas,
        wrapperElement: container
      });
    } catch (e) {
      console.error('[Heart] NextParticle init failed', e);
      const ph = document.getElementById('heart-placeholder');
      if (ph) ph.textContent = '❤';
      return;
    }

    // Replace placeholder
    const ph = document.getElementById('heart-placeholder');
    if (ph && explicitCanvas) {
      ph.replaceWith(explicitCanvas);
    } else if (!container.contains(explicitCanvas)) {
      container.appendChild(explicitCanvas); // safety fallback
    }

    let resizeRaf = 0;
    function resizeHeart() {
      if (!np || !container) return;
      cancelAnimationFrame(resizeRaf);
      resizeRaf = requestAnimationFrame(() => {
        const rect = container.getBoundingClientRect();
        if (rect.width < 5 || rect.height < 5) return; // avoid zero-size restarts
        const size = Math.min(rect.width, rect.height);
        np.width = size; np.height = size; np.maxWidth = size; np.maxHeight = size;
        try { np.stop(); np.start(); } catch (e) { /* ignore transient stop/start errors */ }
      });
    }
    window.addEventListener('resize', resizeHeart);
    resizeHeart();
    currentHeartInstance = np;
    console.log('[Heart] Particle heart initialized (single instance) with color', heartColor);
  })().finally(() => { buildPromise = null; });
  return buildPromise;
}

// Listen for theme changes to rebuild heart with new color
document.addEventListener('theme-changed', async () => {
  if (rebuilding) return; // debounce theme toggles
  rebuilding = true;
  cleanupHeart();
  mountPlaceholder();
  setTimeout(() => {
    initHeartParticle()
      .catch(err => console.warn('[Heart] Re-init error', err))
      .finally(() => { rebuilding = false; });
  }, 60);
});
