// Stable Real-time ECG Visualization (Refactored)
// Approach: deterministic sample generator + circular buffer (ring) for smooth scrolling.

let ecgCtx = null;
let ecgCanvas = null;
let running = false;
let ring = null;        // Float32Array storing normalized y values [-1..1]
let ringSize = 0;       // number of horizontal pixels (logical) used as buffer length
let writeIndex = 0;     // current write head in ring
let lastTs = 0;         // last timestamp
let pxPerSecond = 190;  // horizontal scroll speed
let bpm = 72;           // heart rate
let amplitude = 30;     // vertical scaling in px
let baseline = 0;       // midline y coordinate in canvas
let dpr = window.devicePixelRatio || 1;
let accumulated = 0;    // fractional pixel accumulator for stable speed
let reducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
// Head (end point) live bobbing parameters
let headBobAmplitude = 4;   // px vertical bob (applied only when near baseline)
let headBobSpeed = 6;       // radians per second
// Glow pulse parameters (synced with bob)
let headGlowMin = 5;        // minimum glow radius in px
let headGlowMax = 14;       // maximum glow radius in px
let headGlowAlphaMin = 0.15;
let headGlowAlphaMax = 0.55;
// Spike flash + tail effect
let spikeFlashUntil = 0;    // timestamp (ms) until which flash is active
let spikeFlashDuration = 140; // ms
let tail = [];              // recent head positions for subtle trailing aura
const tailMax = 10;         // number of tail segments
const tailFadeMs = 400;     // lifespan of a tail segment
// Center pulse mode (stationary glowing dot in middle instead of moving head glow)
let centerPulse = false; // follow the moving line endpoint
// Manual pulse injection
let lastManualPulse = 0;
let manualPulseCooldown = 250; // ms minimal gap between manual pulses

function getColors(){
  const dark = document.body.classList.contains('dark');
  return {
    grid: dark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.06)',
    // Reverse mapping to match landing heart: light=green, dark=blue
    trace: dark ? '#3b82f6' /* blue-500 */ : '#22c55e' /* green-500 */,
    glow: dark ? 'rgba(59,130,246,0.55)' : 'rgba(34,197,94,0.55)'
  };
}

function allocateRing(){
  const width = ecgCanvas.clientWidth;
  ringSize = Math.max(200, Math.floor(width));
  ring = new Float32Array(ringSize);
  writeIndex = 0;
  // Pre-fill with baseline zeros
  ring.fill(0);
}

function resize(){
  if(!ecgCanvas) return;
  dpr = window.devicePixelRatio || 1;
  const rect = ecgCanvas.getBoundingClientRect();
  ecgCanvas.width = Math.floor(rect.width * dpr);
  ecgCanvas.height = Math.floor(rect.height * dpr);
  baseline = rect.height / 2; // use CSS pixel height for logic
  if (ecgCtx){
    ecgCtx.setTransform(1,0,0,1,0,0); // reset
    ecgCtx.scale(dpr, dpr);
  }
  allocateRing();
}

// Deterministic ECG waveform generator for one sample at phase (0..1)
function ecgWave(phase){
  // Phase mapping: composite of segments: P (gentle bump), QRS (sharp), T (broad)
  // We'll model with piecewise Gaussians and a spike.
  let y = 0;
  // P wave around 0.1
  y += 0.12 * Math.exp(-Math.pow((phase - 0.12)/0.025, 2));
  // Q dip just before R
  y -= 0.25 * Math.exp(-Math.pow((phase - 0.18)/0.01, 2));
  // R spike
  y += 1.5 * Math.exp(-Math.pow((phase - 0.2)/0.009, 2));
  // S small dip after R
  y -= 0.35 * Math.exp(-Math.pow((phase - 0.23)/0.018, 2));
  // T wave broad hump
  y += 0.3 * Math.exp(-Math.pow((phase - 0.43)/0.07, 2));
  return y; // normalized relative amplitude
}

function writeSamples(pixelAdvance){
  // pixelAdvance may be fractional; accumulate until >=1 then write that many samples
  accumulated += pixelAdvance;
  const msPerBeat = 60000 / bpm;
  while (accumulated >= 1){
    accumulated -= 1;
    // Determine phase based on time progression tied to writeIndex
    const totalPixelsPerBeat = (pxPerSecond * msPerBeat) / 1000;
    const phase = ((writeIndex % totalPixelsPerBeat) / totalPixelsPerBeat);
    const val = ecgWave(phase);
    ring[writeIndex % ringSize] = val;
    writeIndex++;
  }
}

function render(){
  if(!running) return;
  const ctx = ecgCtx; if(!ctx){ requestAnimationFrame(render); return; }
  const width = ecgCanvas.clientWidth;
  const height = ecgCanvas.clientHeight;
  ctx.clearRect(0,0,width,height);
  const { grid, trace, glow } = getColors();

  // Grid: vertical light lines every 20px, horizontal midline
  ctx.save();
  ctx.lineWidth = 1;
  ctx.strokeStyle = grid;
  const gap = 20;
  for(let x=0; x<width; x+=gap){
    ctx.beginPath(); ctx.moveTo(x+0.5,0); ctx.lineTo(x+0.5,height); ctx.stroke();
  }
  ctx.beginPath(); ctx.moveTo(0, baseline+0.5); ctx.lineTo(width, baseline+0.5); ctx.stroke();
  ctx.restore();

  // Trace from oldest to newest.
  ctx.save();
  ctx.beginPath();
  ctx.lineWidth = 2.1;
  ctx.lineJoin = 'round';
  ctx.lineCap = 'round';
  const head = writeIndex; // logical head
  const amp = amplitude;
  // Anchor the live head (dot) at canvas center instead of right edge.
  const centerX = Math.floor(width / 2);
  for(let i=0; i<=centerX; i++){
    // Map i so that i=centerX corresponds to the most recent sample (head-1)
    const ringIndex = (head - 1 - (centerX - i) + ringSize) % ringSize;
    const norm = ring[ringIndex] || 0;
    let y = baseline - norm * amp;
    let headPhase = 0;
    let headIsNearBaseline = false;
    if(i === centerX){
      headIsNearBaseline = Math.abs(norm) < 0.08;
      if(headIsNearBaseline && !reducedMotion){
        const t = performance.now() / 1000;
        const syncedSpeed = (bpm/60) * Math.PI * 2;
        headPhase = Math.sin(t * syncedSpeed);
        y += headPhase * headBobAmplitude;
      }
      if(norm > 1.0 && performance.now() > spikeFlashUntil){
        spikeFlashUntil = performance.now() + spikeFlashDuration;
      }
      render._headY = y;
      render._headX = centerX;
      render._headPhase = headPhase;
      render._headActive = headIsNearBaseline && !reducedMotion;
      render._headNorm = norm;
    }
    if(i===0) ctx.moveTo(i, y); else ctx.lineTo(i, y);
  }
  // Disable tail effect when head is centered (no lateral travel)
  tail.length = 0;
  ctx.shadowColor = glow;
  ctx.shadowBlur = 12;
  ctx.strokeStyle = trace;
  ctx.stroke();
  ctx.restore();

  if(centerPulse){
    // (Optional center mode retained for future toggle) – currently disabled.
  } else if(render._headX != null){
    // Moving endpoint glow (dot sits exactly at line end)
    const now = performance.now();
    const hx = render._headX;
    const hy = render._headY;
    const phase = render._headPhase || 0; // -1..1 bob phase
    const active = render._headActive;
    const pulse = (phase + 1)/2; // 0..1
    const flashProgress = now < spikeFlashUntil ? 1 - ((spikeFlashUntil - now) / spikeFlashDuration) : 0; // 0..1
    const flashEase = flashProgress ? (1 - Math.pow(1 - flashProgress, 3)) : 0;
    const radiusBase = headGlowMin + (headGlowMax - headGlowMin) * pulse;
    const radius = radiusBase + flashEase * 10;
    const alphaBase = (active ? headGlowAlphaMin + (headGlowAlphaMax - headGlowAlphaMin) * pulse : headGlowAlphaMin);
    const alpha = alphaBase + flashEase * 0.35;
    const colors = getColors();
    const headColor = colors.trace;
    const grad = ctx.createRadialGradient(hx, hy, 0, hx, hy, radius);
    const innerColor = flashEase > 0 ? '#ffffff' : headColor;
    grad.addColorStop(0, innerColor);
    grad.addColorStop(0.35, headColor);
    grad.addColorStop(0.7, headColor + '00');
    grad.addColorStop(1, headColor + '00');
    ctx.save();
    ctx.globalCompositeOperation = 'lighter';
    ctx.globalAlpha = alpha;
    ctx.fillStyle = grad;
    ctx.beginPath();
    ctx.arc(hx, hy, radius, 0, Math.PI*2);
    ctx.fill();
    ctx.restore();

    // Tail trail (optional subtle motion continuity)
    if(!reducedMotion){
      tail.push({ x: hx, y: hy, t: now });
      while(tail.length > tailMax) tail.shift();
    }
  }

  // Tail rendering (only when endpoint glow active)
  if(!centerPulse && tail.length){
    const now = performance.now();
    ctx.save();
    ctx.globalCompositeOperation = 'lighter';
    for(let i=0; i<tail.length; i++){
      const seg = tail[i];
      const age = now - seg.t;
      if(age > tailFadeMs) continue;
      const k = 1 - (age / tailFadeMs);
      const r = 3 + k * 10;
      const a = 0.04 + k * 0.12;
      ctx.globalAlpha = a;
      const c = getColors().trace;
      const g = ctx.createRadialGradient(seg.x, seg.y, 0, seg.x, seg.y, r);
      g.addColorStop(0, c);
      g.addColorStop(1, c + '00');
      ctx.fillStyle = g;
      ctx.beginPath();
      ctx.arc(seg.x, seg.y, r, 0, Math.PI*2);
      ctx.fill();
    }
    ctx.restore();
  }

  requestAnimationFrame(render);
}

function step(ts){
  if(!running){ return; }
  if(!lastTs) lastTs = ts;
  const dt = (ts - lastTs) / 1000; // seconds
  lastTs = ts;
  if(!reducedMotion){
    writeSamples(pxPerSecond * dt);
  }
  requestAnimationFrame(step);
}

export function initHeartbeat(options = {}){
  ecgCanvas = document.getElementById('ecg-canvas');
  if(!ecgCanvas) return;
  ecgCtx = ecgCanvas.getContext('2d');
  bpm = options.bpm || bpm;
  amplitude = options.amplitude || amplitude;
  pxPerSecond = options.speed || pxPerSecond;
  reducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  resize();
  running = true; lastTs = 0; accumulated = 0; writeIndex = 0;
  // Prime initial buffer
  writeSamples(ringSize); // fill at least one pass
  requestAnimationFrame(step);
  requestAnimationFrame(render);

  // Attach click to inject a manual pulse
  ecgCanvas.addEventListener('click', () => {
    const now = performance.now();
    if(now - lastManualPulse < manualPulseCooldown) return; // debounce
    lastManualPulse = now;
    injectManualPulse();
  });
}

export function destroyHeartbeat(){
  running = false;
}

// Theme change triggers only redraw (colors fetched each frame), no action needed.
document.addEventListener('theme-changed', () => { /* passive */ });

window.addEventListener('resize', () => { resize(); });

// Reduced motion adjustments
if(reducedMotion){
  pxPerSecond = 0; amplitude = 18; bpm = 60;
}

// ================= Manual Pulse Helpers =================
function injectManualPulse(){
  if(!ring || !ringSize) return;
  const headIndex = (writeIndex - 1 + ringSize) % ringSize;
  // Pattern segments relative to head (samples): [offset, value]
  // Creates a stylized P-Q-R-S-T quickly.
  const pattern = [
    [-6, 0.02],
    [-5, 0.05],
    [-4, 0.12], // P rise
    [-3, 0.0],
    [-2, -0.25], // Q dip
    [-1, 0.0],
    [0, 1.5],    // R spike
    [1, -0.35],  // S dip
    [2, 0.12],
    [3, 0.18],
    [4, 0.25],
    [5, 0.3],    // T hump broad start
    [6, 0.28],
    [7, 0.22],
    [8, 0.12],
    [9, 0.05]
  ];
  for(const [off, val] of pattern){
    const idx = (headIndex + off + ringSize) % ringSize;
    ring[idx] = val;
  }
  // Trigger flash visual
  spikeFlashUntil = performance.now() + spikeFlashDuration;
}
