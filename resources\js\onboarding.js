import './app.js';

document.addEventListener('DOMContentLoaded', () => {
  const form = document.getElementById('onboardingForm');
  if (!form) return;

  const panes = Array.from(document.querySelectorAll('.onb-step-pane'));
  const steps = Array.from(document.querySelectorAll('.onb-step'));
  const progressBar = document.getElementById('progressBar');
  const skipModal = document.getElementById('skipModal');
  const confetti = document.getElementById('confetti');
  let current = 1;

  const total = panes.length;
  // Live validation state
  const bpSys = form.querySelector('input[name="blood_pressure_systolic"]');
  const bpDia = form.querySelector('input[name="blood_pressure_diastolic"]');
  const step2Pane = form.querySelector('.onb-step-pane[data-step="2"]');
  let step2ContinueBtn = step2Pane?.querySelector('[data-next]');

  function getErrorContainer(el) {
    // Always use the same container to avoid duplicates
    return el.closest('.input-group') || el.closest('label') || el.parentElement;
  }

  function setError(el, msg) {
    if (!el) return;
    const container = getErrorContainer(el);
    let err = container.querySelector('.field-error');
    if (!err) {
      err = document.createElement('div');
      err.className = 'field-error';
      container.appendChild(err);
    }
    err.textContent = msg || '';
    err.style.display = msg ? 'block' : 'none';
    el.setAttribute('aria-invalid', msg ? 'true' : 'false');
  }

  const glucose = form.querySelector('input[name="glucose_level"]');
  const spo2 = form.querySelector('input[name="spo2_level"]');

  function validateVitals() {
    let valid = true;
    const sysVal = bpSys && bpSys.value !== '' ? Number(bpSys.value) : null;
    const diaVal = bpDia && bpDia.value !== '' ? Number(bpDia.value) : null;
    const gluVal = glucose && glucose.value !== '' ? Number(glucose.value) : null;
    const spo2Val = spo2 && spo2.value !== '' ? Number(spo2.value) : null;

    // Reset errors
    setError(bpSys, '');
    setError(bpDia, '');
    setError(glucose, '');
    setError(spo2, '');

    // Min constraints
    if (sysVal !== null && sysVal < 60) { setError(bpSys, 'Systolic must be at least 60.'); valid = false; }
    if (diaVal !== null && diaVal < 40) { setError(bpDia, 'Diastolic must be at least 40.'); valid = false; }
    if (gluVal !== null && gluVal < 20) { setError(glucose, 'Glucose must be at least 20.'); valid = false; }
    if (spo2Val !== null && spo2Val < 50) { setError(spo2, 'SpO₂ must be at least 50.'); valid = false; }

    // Relationship constraint (only show if mins are OK and both present)
    if (
      sysVal !== null && diaVal !== null &&
      sysVal >= 60 && diaVal >= 40 &&
      sysVal <= diaVal
    ) {
      setError(bpSys, 'Systolic must be greater than diastolic.');
      valid = false;
    }

    if (step2ContinueBtn) step2ContinueBtn.disabled = !valid;
    return valid;
  }

  bpSys?.addEventListener('input', validateVitals);
  bpDia?.addEventListener('input', validateVitals);
  glucose?.addEventListener('input', validateVitals);
  spo2?.addEventListener('input', validateVitals);
  // Initialize state
  if (step2Pane) validateVitals();

  function updateProgress() {
    const pct = (current / total) * 100;
    progressBar.style.width = pct + '%';
    steps.forEach((el, idx) => {
      const stepNum = idx + 1;
      el.classList.toggle('onb-step--active', stepNum === current);
      el.classList.toggle('onb-step--done', stepNum < current);
    });
  }

  function showStep(step) {
    panes.forEach((pane) => {
      const s = Number(pane.getAttribute('data-step'));
      const active = s === step;
      pane.classList.toggle('onb-step-pane--active', active);
      // Enable inputs for active pane; disable for hidden panes
      const fields = pane.querySelectorAll('input, select, textarea, button');
      fields.forEach(el => {
        if (active) {
          // Re-enable only those we disabled ourselves
          if (el.dataset.onbDisabled === 'true') {
            el.disabled = false;
            delete el.dataset.onbDisabled;
          }
          // Ensure primary action buttons are enabled on the active step
          if (el.id === 'finishBtn' || el.getAttribute('data-next') !== null || el.getAttribute('data-prev') !== null) {
            el.disabled = false;
          }
        } else {
          // Disable elements for hidden panes, but remember only if we changed it
          if (!el.disabled) {
            el.dataset.onbDisabled = 'true';
            el.disabled = true;
          }
        }
      });
    });
    current = step;
    updateProgress();
  }

  document.body.addEventListener('click', (e) => {
    const t = e.target;
    if (t.matches('[data-next]')) {
      e.preventDefault();
      if (current < total) showStep(current + 1);
    }
    if (t.matches('[data-prev]')) {
      e.preventDefault();
      if (current > 1) showStep(current - 1);
    }
    if (t.matches('[data-skip]')) {
      e.preventDefault();
      openModal(skipModal);
    }
    if (t.matches('[data-close]')) {
      e.preventDefault();
      closeModal(skipModal);
    }
  });

  function openModal(el) {
    if (!el) return;
    el.setAttribute('aria-hidden', 'false');
    el.querySelector('.modal__dialog')?.classList.add('modal__dialog--in');
  }
  function closeModal(el) {
    if (!el) return;
    el.setAttribute('aria-hidden', 'true');
    el.querySelector('.modal__dialog')?.classList.remove('modal__dialog--in');
  }

  // Dynamic medications list
  const medList = document.getElementById('medList');
  const addMedication = document.getElementById('addMedication');
  let medIdx = 0;
  function addMedRow() {
    const row = document.createElement('div');
    row.className = 'med-row';
    row.innerHTML = `
      <input name="medications[${medIdx}][name]" placeholder="Name" required />
      <input name="medications[${medIdx}][dosage]" placeholder="Dosage" />
      <input name="medications[${medIdx}][frequency]" placeholder="Frequency" />
      <button type="button" class="icon-btn" aria-label="Remove">✖</button>
    `;
    medIdx++;
    medList?.appendChild(row);
  }
  addMedication?.addEventListener('click', addMedRow);
  medList?.addEventListener('click', (e) => {
    const btn = e.target.closest('button');
    if (btn) btn.parentElement?.remove();
  });

  // Simple celebration on submit success (server redirects, but keep for SPA cases)
  form.addEventListener('submit', () => {
    // Re-enable all fields so server receives all values
    panes.forEach(pane => {
      pane.querySelectorAll('input, select, textarea, button').forEach(el => {
        if (el.dataset.onbDisabled === 'true') {
          el.disabled = false; // we only revert what we disabled
          delete el.dataset.onbDisabled;
        }
      });
    });
    if (confetti) confetti.classList.add('confetti--show');
  });

  // Ensure Save & Finish always submits the form
  const finishBtn = document.getElementById('finishBtn');
  finishBtn?.addEventListener('click', (e) => {
    e.preventDefault();
    // same re-enable logic before programmatic submit
    panes.forEach(pane => {
      pane.querySelectorAll('input, select, textarea, button').forEach(el => {
        if (el.dataset.onbDisabled === 'true') {
          el.disabled = false;
          delete el.dataset.onbDisabled;
        }
      });
    });
    if (typeof form.requestSubmit === 'function') form.requestSubmit(); else form.submit();
  });

  // Initialize
  showStep(1);
});
