@extends('layouts.app')

@section('content')
<style>
  .admin-shell { max-width:1200px; margin:0 auto; padding:16px; color:var(--color-text); }
  .admin-main { display:flex; flex-direction:column; gap:12px; }
  .admin-header { display:flex; align-items:center; justify-content:space-between; gap:10px; background: var(--color-surface); border:1px solid var(--color-border); border-radius:12px; padding:12px 14px; }
  .admin-title { font-size:1.25rem; font-weight:600; margin:0; }
  .admin-actions { display:flex; align-items:center; gap:8px; }
  .card { background: var(--color-surface); border:1px solid var(--color-border); border-radius:12px; padding: 14px; }
  .btn { display:inline-flex; align-items:center; gap:6px; padding:8px 12px; border-radius:8px; border:1px solid var(--color-border); color:var(--color-text); background:var(--color-surface); text-decoration:none; cursor:pointer; }
  .btn:hover { filter: brightness(1.05); }
  .btn-primary { background: var(--color-surface-alt); }
  .btn-danger { border-color:#b91c1c; color:#b91c1c; }
  .btn-outline { background: transparent; }
  /* Admin form helpers */
  .input { background:var(--color-surface-alt); color:var(--color-text); border:1px solid var(--color-border); padding:.85rem 1rem; border-radius:var(--radius-md); width:100%; display:block; box-sizing:border-box; }
  .form-grid { display:grid; grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); gap:16px; }
  .form-field { display:flex; flex-direction:column; }
  .form-label { display:block; font-size:.85rem; line-height:1.2; margin:0 0 .5rem; color:var(--color-text); opacity:.9; overflow-wrap:anywhere; word-break:break-word; }
  .table { width:100%; border-collapse:separate; border-spacing:0; }
  .table th, .table td { padding:10px 12px; border-bottom:1px solid var(--color-border); text-align:left; }
  .table thead th { background: var(--color-surface-alt); position: sticky; top:0; z-index:1; }
  .table tbody tr:nth-child(odd) td { background: color-mix(in srgb, var(--color-surface) 92%, var(--color-surface-alt)); }
  .table tr:hover td { background: color-mix(in srgb, var(--color-surface-alt) 40%, transparent); }
  @supports not (color: color-mix(in srgb, #000 10%, #fff)) { .table tr:hover td { background: var(--color-surface-alt); } }
  .badge { display:inline-block; padding:2px 8px; border-radius:999px; font-size:.75rem; line-height:1.6; border:1px solid var(--color-border); }
  .badge.green { background:#16a34a20; border-color:#16a34a55; color:#16a34a; }
  .badge.red { background:#b91c1c20; border-color:#b91c1c55; color:#b91c1c; }
  .muted { opacity:.8; }
</style>

<div class="admin-shell">
  <section class="admin-main">
    <header class="admin-header">
      <h2 class="admin-title">@yield('admin-title','Admin')</h2>
      <div class="admin-actions">@yield('admin-actions')</div>
    </header>

    <div class="card">
      @yield('admin')
    </div>
  </section>
</div>
@endsection
