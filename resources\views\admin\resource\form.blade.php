@extends('admin.layout')

@section('admin-title', ($isCreate ? 'Create' : 'Edit').' '.ucfirst(str_replace('-', ' ', $resource)))

@section('admin')
<div style="display:flex; align-items:center; justify-content:flex-end; gap:10px; margin-bottom:10px;">
  <a href="{{ route('admin.resource.index', [$resource]) }}" class="btn">Back</a>
  </div>

@if (session('status'))
  <div class="mb-3" style="padding:8px 10px; border-radius:8px; background:#16a34a20; border:1px solid #16a34a55; color:var(--color-text);">{{ session('status') }}</div>
@endif

<form method="post" action="{{ $isCreate ? route('admin.resource.store', [$resource]) : route('admin.resource.update', [$resource, $item->getKey()]) }}">
  @csrf
  @if(!$isCreate)
    @method('put')
  @endif

  <div class="form-grid">
    @foreach($fields as $field)
      @php($inputId = 'fld_' . $field)
      @php($type = match(true) {
        str_contains($field, 'email') => 'email',
        str_contains($field, 'date') => 'date',
        str_contains($field, 'phone') => 'tel',
        str_contains($field, 'number') => 'number',
        default => 'text',
      })
      @php($isLong = in_array($field, ['health_history','allergies','medical_conditions','address','notes','message']))
      <label class="form-field" for="{{ $inputId }}">
        <div class="form-label">{{ ucwords(str_replace('_',' ', $field)) }}</div>
        @if($isLong)
          <textarea class="input" id="{{ $inputId }}" name="{{ $field }}">{{ old($field, $item->{$field}) }}</textarea>
        @else
          <input class="input" type="{{ $type }}" id="{{ $inputId }}" name="{{ $field }}" value="{{ old($field, $item->{$field}) }}" />
        @endif
      </label>
    @endforeach
    @if($resource === 'users')
      <label class="form-field" for="fld_password">
        <div class="form-label">Password (optional)</div>
        <input class="input" id="fld_password" type="password" name="password" />
      </label>
    @endif
  </div>

  <div style="margin-top:12px; display:flex; gap:8px;">
    <button type="submit" class="btn btn-primary">Save</button>
    <a href="{{ route('admin.resource.index', [$resource]) }}" class="btn">Cancel</a>
  </div>
</form>
@endsection
