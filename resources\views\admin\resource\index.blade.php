@extends('admin.layout')

@section('admin-title', ucfirst(str_replace('-', ' ', $resource)))
@section('admin-actions')
  <a href="{{ route('admin.resource.create', [$resource]) }}" class="btn btn-primary">Create</a>
@endsection

@section('admin')
<div style="display:flex; align-items:center; justify-content:flex-start; gap:10px; margin-bottom:10px; flex-wrap:wrap;">
  <form method="get" style="display:flex; gap:8px; align-items:center;">
    <label class="sr-only" for="q">Search</label>
    <input id="q" type="text" name="q" value="{{ request('q') }}" placeholder="Search..." class="input" autocomplete="off">
    <button class="btn btn-primary" type="submit">Filter</button>
  </form>
</div>

@if (session('status'))
  <div class="card" role="status" style="margin-bottom:10px; border-left: 4px solid #16a34a;">
    {{ session('status') }}
  </div>
@endif

<div class="card" style="padding:0; overflow:auto;">
  <table class="table">
    <thead>
      <tr>
        @if(!empty($columns))
          @foreach($columns as $col)
            <th>{{ $col }}</th>
          @endforeach
          <th>Actions</th>
        @else
          <th>No data</th>
        @endif
      </tr>
    </thead>
    <tbody>
      @foreach($items as $row)
      <tr>
        @foreach($columns as $col)
          <td title="{{ (string) data_get($row, $col) }}">{{ (string) data_get($row, $col) }}</td>
        @endforeach
        <td style="white-space:nowrap;">
          <a class="btn btn-outline" href="{{ route('admin.resource.edit', [$resource, $row->getKey()]) }}">Edit</a>
          <form method="post" action="{{ route('admin.resource.destroy', [$resource, $row->getKey()]) }}" style="display:inline;" onsubmit="return confirm('Delete this record?');">
            @csrf
            @method('delete')
            <button type="submit" class="btn" style="color:#b91c1c; border-color:#b91c1c;">Delete</button>
          </form>
        </td>
      </tr>
      @endforeach
    </tbody>
  </table>
</div>

<div style="margin-top:10px;">{{ $items->withQueryString()->links() }}</div>
@endsection
