<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>Sign In – Healthcare Tracker</title>
    <meta name="description" content="Sign in to your Healthcare Tracker account." />
    @vite(['resources/css/app.css','resources/js/app.js'])
</head>
<body class="antialiased font-sans bg-base text-base-text dark:bg-dark-base dark:text-dark-text auth-wrapper">
    <div class="auth-gradient" aria-hidden="true"></div>
    <canvas id="bg-canvas" aria-hidden="true"></canvas>
    <header class="site-header" style="background:transparent;border:0;box-shadow:none;">
        <nav class="nav">
            <div class="nav__logo">💚 <span>Healthcare<span class="accent">Tracker</span></span></div>
            <a href="/" class="btn btn--ghost" style="margin-left:auto">← Home</a>
        </nav>
    </header>

    <main class="section auth-main" data-animate="fade-up">
        <div style="max-width:440px;margin:0 auto; position:relative;">
            <h1 class="section__title" style="text-align:center;font-size:clamp(2rem,4vw,2.4rem);">Welcome back</h1>
            <p class="section__subtitle" style="text-align:center;">Access your unified health dashboard.</p>

            <form class="feature-card auth-card" style="margin-top:2rem;" method="POST" action="{{ route('login.attempt') }}" aria-label="Login form" novalidate>
                @csrf
                @if (session('status'))
                    <div class="onb-success" role="status" style="margin-bottom:1rem;">
                        {{ session('status') }}
                    </div>
                @endif
                @if ($errors->any())
                    <div class="onb-error" role="alert" style="margin-bottom:1rem;">
                        <strong>We found some issues:</strong>
                        <ul>
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif
                <div style="display:flex;flex-direction:column;gap:1rem;">
                    <label style="display:flex;flex-direction:column;gap:.4rem;font-size:.85rem;">
                        <span>Email</span>
                        <input name="email" type="email" value="{{ old('email') }}" autocomplete="email" required placeholder="<EMAIL>" aria-invalid="{{ $errors->has('email') ? 'true' : 'false' }}" style="padding:.85rem 1rem;border:1px solid var(--color-border);border-radius:var(--radius-md);background:var(--color-surface-alt);" />
                        @error('email')
                        <small class="text-2xs" style="color:#e11d48;">{{ $message }}</small>
                        @enderror
                    </label>
                    <label style="display:flex;flex-direction:column;gap:.4rem;font-size:.85rem;">
                        <span>Password</span>
                        <input name="password" type="password" autocomplete="current-password" required placeholder="••••••••" minlength="8" aria-invalid="{{ $errors->has('password') ? 'true' : 'false' }}" style="padding:.85rem 1rem;border:1px solid var(--color-border);border-radius:var(--radius-md);background:var(--color-surface-alt);" />
                        @error('password')
                        <small class="text-2xs" style="color:#e11d48;">{{ $message }}</small>
                        @enderror
                    </label>
                    <div style="display:flex;justify-content:space-between;align-items:center;font-size:.7rem;">
                        <label style="display:flex;align-items:center;gap:.4rem;">
                            <input type="checkbox" name="remember" /> <span>Remember me</span>
                        </label>
                        <a href="{{ route('password.forgot') }}" style="color:var(--color-primary);text-decoration:none;">Forgot password?</a>
                    </div>
                    <button class="btn btn--primary-solid btn--lg btn--center" data-micro="bounce" type="submit">Sign In</button>
                    <p style="font-size:.65rem;opacity:.7;text-align:center;margin:0;">If your email isn't verified yet you'll be redirected to resend the verification link.</p>
                </div>
            </form>
            <p style="text-align:center;margin-top:1.5rem;font-size:.85rem;">New here? <a href="{{ route('register') }}" style="color:var(--color-primary);text-decoration:none;">Create an account</a></p>
        </div>
    </main>
</body>
</html>
