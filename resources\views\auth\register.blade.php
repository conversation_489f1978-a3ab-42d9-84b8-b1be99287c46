<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>Create Account – Healthcare Tracker</title>
    <meta name="description" content="Create your Healthcare Tracker account." />
    @vite(['resources/css/app.css','resources/js/app.js'])
</head>
<body class="antialiased font-sans bg-base text-base-text dark:bg-dark-base dark:text-dark-text auth-wrapper">
    <div class="auth-gradient" aria-hidden="true"></div>
    <canvas id="bg-canvas" aria-hidden="true"></canvas>
    <header class="site-header" style="background:transparent;border:0;box-shadow:none;">
        <nav class="nav">
            <div class="nav__logo">💚 <span>Healthcare<span class="accent">Tracker</span></span></div>
            <a href="/" class="btn btn--ghost" style="margin-left:auto">← Home</a>
        </nav>
    </header>

    <main class="section auth-main" data-animate="fade-up">
        <div style="max-width:480px;margin:0 auto; position:relative;">
            <h1 class="section__title" style="text-align:center;font-size:clamp(2rem,4vw,2.5rem);">Create your account</h1>
            <p class="section__subtitle" style="text-align:center;">Join thousands taking control of their wellbeing.</p>

            <form class="feature-card auth-card" style="margin-top:2rem;" method="POST" action="{{ route('register.store') }}" aria-label="Register form" id="registerForm" novalidate>
                @csrf
                @if ($errors->any())
                    <div class="onb-error" role="alert" style="margin-bottom:1rem;">
                        <strong>We found some issues:</strong>
                        <ul>
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif
                <div style="display:flex;flex-direction:column;gap:1.35rem;">
                    <!-- Step 1: Role Selection -->
                    <fieldset style="border:0; margin:0; padding:0; display:flex; flex-direction:column; gap:.65rem;">
                        
                        <div class="role-switch" style="display:flex; gap:.75rem; flex-wrap:wrap;">
                            <label class="role-option" style="display:flex; align-items:center; gap:.45rem; background:var(--color-surface-alt); padding:.6rem .85rem; border:1px solid var(--color-border); border-radius:var(--radius-pill); font-size:.75rem; cursor:pointer;">
                                <input type="radio" name="role" value="patient" {{ old('role','patient')==='patient'?'checked':'' }} /> <span>Patient</span>
                            </label>
                            <label class="role-option" style="display:flex; align-items:center; gap:.45rem; background:var(--color-surface-alt); padding:.6rem .85rem; border:1px solid var(--color-border); border-radius:var(--radius-pill); font-size:.75rem; cursor:pointer;">
                                <input type="radio" name="role" value="doctor" {{ old('role')==='doctor'?'checked':'' }} /> <span>Doctor</span>
                            </label>
                        </div>
                    </fieldset>

                    <!-- Step 2: Common Fields -->
                    <div class="form-grid" style="display:grid; gap:1rem;">
                        <label style="display:flex;flex-direction:column;gap:.4rem;font-size:.8rem;">
                            <span>Name</span>
                            <input name="name" type="text" value="{{ old('name') }}" required placeholder="Your full name" class="form-control" autocomplete="name" aria-invalid="{{ $errors->has('name') ? 'true' : 'false' }}" />
                            @error('name')<small class="text-2xs" style="color:#e11d48;">{{ $message }}</small>@enderror
                        </label>
                        <label style="display:flex;flex-direction:column;gap:.4rem;font-size:.8rem;">
                            <span>Email</span>
                            <input name="email" type="email" value="{{ old('email') }}" required placeholder="<EMAIL>" class="form-control" autocomplete="email" aria-invalid="{{ $errors->has('email') ? 'true' : 'false' }}" />
                            @error('email')<small class="text-2xs" style="color:#e11d48;">{{ $message }}</small>@enderror
                        </label>
                        <label style="display:flex;flex-direction:column;gap:.4rem;font-size:.8rem;">
                            <span>Password</span>
                            <input name="password" type="password" required minlength="8" placeholder="••••••••" class="form-control" autocomplete="new-password" aria-invalid="{{ $errors->has('password') ? 'true' : 'false' }}" />
                            @error('password')<small class="text-2xs" style="color:#e11d48;">{{ $message }}</small>@enderror
                        </label>
                        <label style="display:flex;flex-direction:column;gap:.4rem;font-size:.8rem;">
                            <span>Confirm Password</span>
                            <input name="password_confirmation" type="password" required minlength="8" placeholder="••••••••" class="form-control" autocomplete="new-password" aria-invalid="{{ $errors->has('password_confirmation') ? 'true' : 'false' }}" />
                            @error('password_confirmation')<small class="text-2xs" style="color:#e11d48;">{{ $message }}</small>@enderror
                        </label>
                        <label style="display:flex;flex-direction:column;gap:.4rem;font-size:.8rem;">
                            <span>Phone (optional)</span>
                            <input name="phone" type="tel" value="{{ old('phone') }}" placeholder="+216 11 222 333" class="form-control" autocomplete="tel" aria-invalid="{{ $errors->has('phone') ? 'true' : 'false' }}" />
                            @error('phone')<small class="text-2xs" style="color:#e11d48;">{{ $message }}</small>@enderror
                        </label>
                    </div>

                    <!-- Step 3: Patient Fields -->
                    <div id="patientFields" class="role-fields" data-role="patient" style="display:grid; gap:1rem;">
                        <div style="display:grid; gap:1rem; grid-template-columns:repeat(auto-fit,minmax(180px,1fr));">
                            <label style="display:flex;flex-direction:column;gap:.4rem;font-size:.8rem;">
                                <span>Date of Birth</span>
                                <input name="dob" type="date" class="form-control" value="{{ old('dob') }}" autocomplete="bday" aria-invalid="{{ $errors->has('dob') ? 'true' : 'false' }}" />
                                @error('dob')<small class="text-2xs" style="color:#e11d48;">{{ $message }}</small>@enderror
                            </label>
                            <label style="display:flex;flex-direction:column;gap:.4rem;font-size:.8rem;">
                                <span>Gender</span>
                                <select name="gender" class="form-control" autocomplete="sex" aria-invalid="{{ $errors->has('gender') ? 'true' : 'false' }}">
                                    <option value="" disabled selected>Select</option>
                                    <option {{ old('gender')==='Female'?'selected':'' }}>Female</option>
                                    <option {{ old('gender')==='Male'?'selected':'' }}>Male</option>
                                    <option {{ old('gender')==='Other'?'selected':'' }}>Other</option>
                                    <option {{ old('gender')==='Prefer not to say'?'selected':'' }}>Prefer not to say</option>
                                </select>
                                @error('gender')<small class="text-2xs" style="color:#e11d48;">{{ $message }}</small>@enderror
                            </label>
                        </div>
                        <label style="display:flex;flex-direction:column;gap:.4rem;font-size:.8rem;">
                            <span>Address</span>
                            <input name="address" type="text" value="{{ old('address') }}" placeholder="Street, City, Country" class="form-control" autocomplete="street-address" aria-invalid="{{ $errors->has('address') ? 'true' : 'false' }}" />
                            @error('address')<small class="text-2xs" style="color:#e11d48;">{{ $message }}</small>@enderror
                        </label>
                        <label style="display:flex;flex-direction:column;gap:.4rem;font-size:.8rem;">
                            <span>Emergency Contact</span>
                            <input name="emergency_contact" type="text" value="{{ old('emergency_contact') }}" placeholder="Name & phone" class="form-control" autocomplete="tel" aria-invalid="{{ $errors->has('emergency_contact') ? 'true' : 'false' }}" />
                            @error('emergency_contact')<small class="text-2xs" style="color:#e11d48;">{{ $message }}</small>@enderror
                        </label>
                        <details style="font-size:.7rem;">
                            <summary style="cursor:pointer; color:var(--color-primary);">Optional: Insurance & Health History</summary>
                            <div style="display:grid; gap:.75rem; margin-top:.75rem;">
                                <label style="display:flex;flex-direction:column;gap:.35rem;font-size:.75rem;">
                                    <span>Insurance Provider</span>
                                    <input name="insurance_provider" type="text" value="{{ old('insurance_provider') }}" class="form-control" />
                                </label>
                                <label style="display:flex;flex-direction:column;gap:.35rem;font-size:.75rem;">
                                    <span>Policy Number</span>
                                    <input name="policy_number" type="text" value="{{ old('policy_number') }}" class="form-control" />
                                </label>
                                <label style="display:flex;flex-direction:column;gap:.35rem;font-size:.75rem;">
                                    <span>Health History Summary</span>
                                    <textarea name="health_history" rows="3" class="form-control" placeholder="Chronic conditions, allergies, etc">{{ old('health_history') }}</textarea>
                                </label>
                            </div>
                        </details>
                    </div>

                    <!-- Step 3: Doctor Fields -->
                    <div id="doctorFields" class="role-fields" data-role="doctor" style="display:none; gap:1rem;">
                        <div style="display:grid; gap:1rem; grid-template-columns:repeat(auto-fit,minmax(180px,1fr));">
                            <label style="display:flex;flex-direction:column;gap:.4rem;font-size:.8rem;">
                                <span>Specialty</span>
                                <input name="specialty" type="text" value="{{ old('specialty') }}" placeholder="e.g. Cardiology" class="form-control" />
                            </label>
                            <label style="display:flex;flex-direction:column;gap:.4rem;font-size:.8rem;">
                                <span>License Number</span>
                                <input name="license_number" type="text" value="{{ old('license_number') }}" class="form-control" />
                            </label>
                        </div>
                        <label style="display:flex;flex-direction:column;gap:.4rem;font-size:.8rem;">
                            <span>Hospital Affiliation</span>
                            <input name="hospital" type="text" value="{{ old('hospital') }}" placeholder="Primary institution" class="form-control" />
                        </label>
                        <label style="display:flex;flex-direction:column;gap:.4rem;font-size:.8rem;">
                            <span>Years of Experience</span>
                            <input name="experience_years" type="number" value="{{ old('experience_years') }}" min="0" class="form-control" />
                        </label>
                    </div>

                    <label style="display:flex;align-items:center;gap:.55rem;font-size:.7rem;">
                        <input type="checkbox" id="terms" name="terms" {{ old('terms')?'checked':'' }} required /> <span>I agree to the <a href="#" style="color:var(--color-primary);text-decoration:none;">Terms</a> & Privacy.</span>
                        @error('terms')<small class="text-2xs" style="color:#e11d48;">{{ $message }}</small>@enderror
                    </label>
                    <button class="btn btn--primary-solid btn--lg btn--center" data-micro="bounce" type="submit">Create Account</button>
                    <p style="font-size:.65rem;opacity:.7;text-align:center;margin:0;">After creating your account you'll receive a verification email. Verify it to access your personalized baseline setup.</p>
                </div>
            </form>
            <p style="text-align:center;margin-top:1.5rem;font-size:.85rem;">Already have an account? <a href="{{ route('login') }}" style="color:var(--color-primary);text-decoration:none;">Sign in</a></p>
        </div>
    </main>
</body>
</html>
