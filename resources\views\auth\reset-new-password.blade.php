<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>Set New Password – Healthcare Tracker</title>
    <meta name="description" content="Choose a new password for your account." />
    @vite(['resources/css/app.css','resources/js/app.js'])
</head>
<body class="antialiased font-sans bg-base text-base-text dark:bg-dark-base dark:text-dark-text auth-wrapper">
<div class="auth-gradient" aria-hidden="true"></div>
<canvas id="bg-canvas" aria-hidden="true"></canvas>
<header class="site-header" style="background:transparent;border:0;box-shadow:none;">
    <nav class="nav">
        <div class="nav__logo">💚 <span>Healthcare<span class="accent">Tracker</span></span></div>
        <a href="/" class="btn btn--ghost" style="margin-left:auto">← Home</a>
    </nav>
</header>

<main class="section auth-main" data-animate="fade-up">
    <div style="max-width:440px;margin:0 auto; position:relative;">
        <h1 class="section__title" style="text-align:center;font-size:clamp(2rem,4vw,2.4rem);">Set a new password</h1>
        <p class="section__subtitle" style="text-align:center;">Make it strong and unique. You'll sign in after this.</p>

        <form class="feature-card auth-card" style="margin-top:1rem;" method="POST" action="{{ route('password.new.post') }}" novalidate>
            @csrf
            @if ($errors->any())
                <div class="onb-error" role="alert" style="margin-bottom:1rem;">
                    <strong>We found some issues:</strong>
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
            <input type="hidden" name="email" value="{{ $email }}" />
            <div style="display:flex;flex-direction:column;gap:1rem;">
                <label style="display:flex;flex-direction:column;gap:.4rem;font-size:.85rem;">
                    <span>New password</span>
                    <input name="password" type="password" autocomplete="new-password" required minlength="8" placeholder="••••••••" style="padding:.85rem 1rem;border:1px solid var(--color-border);border-radius:var(--radius-md);background:var(--color-surface-alt);color:var(--color-text);" />
                </label>
                <label style="display:flex;flex-direction:column;gap:.4rem;font-size:.85rem;">
                    <span>Confirm password</span>
                    <input name="password_confirmation" type="password" autocomplete="new-password" required minlength="8" placeholder="••••••••" style="padding:.85rem 1rem;border:1px solid var(--color-border);border-radius:var(--radius-md);background:var(--color-surface-alt);color:var(--color-text);" />
                </label>
                <button class="btn btn--primary-solid btn--lg btn--center" data-micro="bounce" type="submit">Reset password</button>
                <p style="font-size:.75rem;opacity:.8;text-align:center;">This will sign you out on other devices.</p>
            </div>
        </form>
    </div>
</main>
</body>
</html>
