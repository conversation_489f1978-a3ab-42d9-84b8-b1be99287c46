@php($status = session('status'))
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Reset Password</title>
  @vite(['resources/css/app.css','resources/js/app.js'])
  <style>.container{max-width:480px;margin:48px auto;padding:24px;background:#fff;border-radius:12px;box-shadow:0 6px 24px rgba(0,0,0,0.08)}.field{margin:12px 0}.error{color:#dc2626;font-size:0.9rem}</style>
  </head>
<body class="bg-base text-base-text dark:bg-dark-base dark:text-dark-text">
  <div class="container">
    <h1 class="text-2xl font-semibold mb-2">Reset your password</h1>
    <p class="mb-4">Enter the 6-digit code we emailed you and choose a new password.</p>
    @if($status)
      <div class="mb-3 text-green-700">{{ $status }}</div>
    @endif
    <form method="post" action="{{ route('password.reset.post') }}">
      @csrf
      <div class="field">
        <label for="email" class="block mb-1">Email</label>
        <input id="email" name="email" type="email" class="w-full" required value="{{ old('email', $email ?? '') }}" />
        @error('email')<div class="error">{{ $message }}</div>@enderror
      </div>
      <div class="field">
        <label for="code" class="block mb-1">Verification Code</label>
        <input id="code" name="code" type="text" class="w-full" required inputmode="numeric" pattern="\\d{6}" placeholder="123456" value="{{ old('code') }}" />
        @error('code')<div class="error">{{ $message }}</div>@enderror
      </div>
      <div class="field">
        <label for="password" class="block mb-1">New Password</label>
        <input id="password" name="password" type="password" class="w-full" required minlength="8" />
        @error('password')<div class="error">{{ $message }}</div>@enderror
      </div>
      <div class="field">
        <label for="password_confirmation" class="block mb-1">Confirm Password</label>
        <input id="password_confirmation" name="password_confirmation" type="password" class="w-full" required minlength="8" />
      </div>
      <div class="mt-4 flex gap-2 items-center">
        <button class="btn btn--primary-solid" type="submit">Reset Password</button>
        <a id="resendLink" class="btn btn--outline" href="{{ route('password.forgot') }}">Resend Code</a>
        <span id="resendCountdown" style="font-size:.85rem;opacity:.7;display:none;">Resend in <span id="resendSecs">60</span>s</span>
      </div>
    </form>
  </div>
  <script>
    // Simple 60s countdown after landing on reset page if coming from forgot
    (function(){
      const qs = new URLSearchParams(window.location.search);
      const fromForgot = qs.get('from') === 'forgot';
      const link = document.getElementById('resendLink');
      const cd = document.getElementById('resendCountdown');
      const secsEl = document.getElementById('resendSecs');
      if (!link || !cd || !secsEl) return;
      let secs = 60;
      function tick(){
        if (secs <= 0) { link.style.pointerEvents='auto'; link.classList.remove('is-disabled'); cd.style.display='none'; return; }
        secsEl.textContent = String(secs);
        secs -= 1;
        setTimeout(tick, 1000);
      }
      // Disable immediately and start countdown if from forgot
      if (fromForgot) {
        link.style.pointerEvents='none';
        link.classList.add('is-disabled');
        cd.style.display='inline';
        tick();
      }
    })();
  </script>
  </body>
  </html>
