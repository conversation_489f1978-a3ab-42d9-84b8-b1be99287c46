<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Password Updated – Healthcare Tracker</title>
  <meta name="description" content="Your password was changed successfully." />
  @vite(['resources/css/app.css','resources/js/app.js'])
  <style>
    .success-hero { display:grid; place-items:center; min-height:60vh; width:100%; }
    .success-card { background:var(--color-surface); border:1px solid var(--color-border); border-radius:1.4rem; padding:2rem; box-shadow:var(--shadow-lg); text-align:center; max-width:520px; margin:0 auto; }
    body.dark .success-card { background:var(--color-surface-alt); }
    .checkmark {
      width:96px; height:96px; border-radius:50%; display:inline-grid; place-items:center;
      background:conic-gradient(from 0deg at 50% 50%, #22c55e, #16a34a);
      box-shadow:0 6px 24px rgba(34,197,94,.35);
      position:relative; isolation:isolate;
      animation: pop .6s cubic-bezier(.19,1,.22,1);
    }
    .checkmark::before {
      content:""; position:absolute; inset:6px; background:var(--color-surface); border-radius:50%; z-index:0;
    }
    body.dark .checkmark::before { background:var(--color-surface-alt); }
    .checkmark svg { width:56px; height:56px; z-index:1; color:#16a34a; }
    @keyframes pop { 0% { transform:scale(.6); opacity:.2 } 100% { transform:scale(1); opacity:1 } }

    /* lightweight confetti */
    .confetti { position:fixed; inset:0; pointer-events:none; overflow:hidden; }
    .confetti i { position:absolute; width:8px; height:14px; opacity:.9; transform:translateY(-100vh) rotate(0deg); animation: drop 1.8s ease-in forwards; }
    @keyframes drop { to { transform:translateY(110vh) rotate(720deg) } }
  </style>
</head>
<body class="antialiased font-sans bg-base text-base-text dark:bg-dark-base dark:text-dark-text auth-wrapper">
  <div class="auth-gradient" aria-hidden="true"></div>
  <canvas id="bg-canvas" aria-hidden="true"></canvas>
  <header class="site-header" style="background:transparent;border:0;box-shadow:none;">
    <nav class="nav">
      <div class="nav__logo">💚 <span>Healthcare<span class="accent">Tracker</span></span></div>
      <a href="/" class="btn btn--ghost" style="margin-left:auto">← Home</a>
    </nav>
  </header>

  <main class="section auth-main" style="justify-content:center;">
    <div class="success-hero">
      <div class="success-card">
        <div class="checkmark" aria-hidden="true">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
            <path d="M20 6L9 17l-5-5"/>
          </svg>
        </div>
        <h1 class="section__title" style="margin-top:1rem;">Password updated</h1>
        <p class="section__subtitle" style="margin-top:.35rem;">{{ $message }}</p>
        <a href="{{ $next }}" class="btn btn--primary-solid btn--lg btn--center" style="margin-top:1.2rem;">Go to Sign In</a>
        <p style="font-size:.8rem;opacity:.75;margin-top:.6rem;">Redirecting in <span id="count">3</span>…</p>
      </div>
    </div>
  </main>

  <div class="confetti" id="confetti" aria-hidden="true"></div>

  <script>
  // Auto redirect after 3 seconds
  (function(){
    const next = "{{ $next }}";
    let s = 3; const el = document.getElementById('count');
    const t = setInterval(()=>{ s--; if (el) el.textContent = String(s); if (s <= 0) { clearInterval(t); window.location.href = next; } }, 1000);
  })();

  // Minimal, performance-friendly confetti (no deps)
  (function(){
    const wrap = document.getElementById('confetti');
    if (!wrap) return;
    const colors = ['#22c55e','#38bdf8','#f97316','#eab308','#a78bfa','#ef4444'];
    const n = 60;
    for (let i=0;i<n;i++) {
      const piece = document.createElement('i');
      const c = colors[Math.floor(Math.random()*colors.length)];
      const left = Math.random()*100; const delay = Math.random()*0.6; const dur = 1.4 + Math.random()*1.2;
      piece.style.left = left+'%';
      piece.style.background = c;
      piece.style.animationDelay = delay+'s';
      piece.style.animationDuration = dur+'s';
      piece.style.borderRadius = (Math.random()>.5?'2px':'6px');
      wrap.appendChild(piece);
    }
    setTimeout(()=>{ wrap.innerHTML = ''; }, 2500);
  })();
  </script>
</body>
</html>
