<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>Verify Code – Healthcare Tracker</title>
    <meta name="description" content="Enter the 6‑digit code sent to your email." />
    @vite(['resources/css/app.css','resources/js/app.js'])
</head>
<body class="antialiased font-sans bg-base text-base-text dark:bg-dark-base dark:text-dark-text auth-wrapper">
<div class="auth-gradient" aria-hidden="true"></div>
<canvas id="bg-canvas" aria-hidden="true"></canvas>
<header class="site-header" style="background:transparent;border:0;box-shadow:none;">
    <nav class="nav">
        <div class="nav__logo">💚 <span>Healthcare<span class="accent">Tracker</span></span></div>
        <a href="/" class="btn btn--ghost" style="margin-left:auto">← Home</a>
    </nav>
</header>

<main class="section auth-main" data-animate="fade-up">
    <div style="max-width:440px;margin:0 auto; position:relative;">
        <h1 class="section__title" style="text-align:center;font-size:clamp(2rem,4vw,2.4rem);">Verify your code</h1>
        <p class="section__subtitle" style="text-align:center;">We sent a 6‑digit code to your email.</p>

        @if (session('status'))
            <div class="onb-success" role="status" style="margin:.75rem 0 0;">{{ session('status') }}</div>
        @endif

        <form class="feature-card auth-card" style="margin-top:1rem;" method="POST" action="{{ route('password.verify.post') }}" novalidate>
            @csrf
            @if ($errors->any())
                <div class="onb-error" role="alert" style="margin-bottom:1rem;">
                    <strong>We found some issues:</strong>
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
            <div style="display:flex;flex-direction:column;gap:1rem;">
                <input type="hidden" name="email" value="{{ old('email', $email ?? '') }}" />
                <label style="display:flex;flex-direction:column;gap:.4rem;font-size:.85rem;">
                    <span>Verification code</span>
                    <input name="code" inputmode="numeric" pattern="[0-9]{6}" maxlength="6" minlength="6" required placeholder="000000" value="{{ old('code') }}" style="letter-spacing:.3em;padding:.85rem 1rem;border:1px solid var(--color-border);border-radius:var(--radius-md);background:var(--color-surface-alt);text-align:center;font-weight:600;color:var(--color-text);" />
                    <small style="opacity:.75;">Code expires in {{ $ttl ?? 5 }} minutes.</small>
                </label>
                <div style="display:flex;justify-content:space-between;align-items:center;font-size:.75rem;opacity:.85;">
                    <a href="{{ route('password.forgot') }}?email={{ urlencode($email ?? '') }}" style="color:var(--color-primary);text-decoration:none;">Resend code</a>
                    <a href="{{ route('login') }}" style="color:var(--color-primary);text-decoration:none;">Back to sign in</a>
                </div>
                <button class="btn btn--primary-solid btn--lg btn--center" data-micro="bounce" type="submit">Continue</button>
            </div>
        </form>
    </div>
</main>
</body>
</html>
