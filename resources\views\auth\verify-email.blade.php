@php($status = session('status'))
<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <title>Verify Your Email</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    @vite(['resources/css/app.css','resources/js/app.js'])
    <style>
        .verify-container {max-width:520px;margin:4rem auto;padding:2.5rem;background:var(--surface,#fff);border-radius:1.25rem;box-shadow:0 8px 28px -6px rgba(0,0,0,.15),0 0 0 1px rgba(255,255,255,.4) inset;position:relative;}
        .verify-container h1 {font-size:1.6rem;margin-bottom:.75rem;}
        .verify-container p {line-height:1.5;color:var(--text-subtle,#555);}
        .status-chip {display:inline-block;padding:.35rem .7rem;font-size:.75rem;border-radius:2rem;background:linear-gradient(135deg,#16a34a,#15803d);color:#fff;margin-top:.75rem;letter-spacing:.5px;}
        form.inline {display:inline-block;margin:0;}
        .divider {height:1px;background:linear-gradient(90deg,transparent,rgba(0,0,0,.15),transparent);margin:1.75rem 0;}
        .resend-btn {background:var(--primary,#2563eb);color:#fff;padding:.75rem 1.1rem;border-radius:.65rem;font-weight:600;display:inline-flex;align-items:center;gap:.5rem;transition:.25s box-shadow, .25s background;}
        .resend-btn:hover {box-shadow:0 4px 18px -4px rgba(0,0,0,.35);}        
        .logout-link {font-size:.8rem;text-transform:uppercase;letter-spacing:1px;font-weight:600;color:var(--primary,#2563eb);}       
        .hint {font-size:.75rem;text-transform:uppercase;letter-spacing:1px;font-weight:600;color:var(--primary,#2563eb);margin-bottom:.5rem;}
        .steps {display:grid;gap:1rem;margin-top:1.25rem;}
        .step {display:flex;gap:.75rem;padding:.9rem 1rem;border:1px solid rgba(0,0,0,.07);border-radius:.9rem;align-items:flex-start;background:linear-gradient(135deg,var(--bg-step,#f8fafc),#fff);position:relative;}
        .step:before {content:"";width:.75rem;height:.75rem;border-radius:50%;margin-top:.35rem;background:linear-gradient(135deg,#2563eb,#1d4ed8);} 
        [data-theme="dark"] .verify-container {background:#111827;box-shadow:0 0 0 1px rgba(255,255,255,.08),0 10px 32px -8px rgba(0,0,0,.6);}        
        [data-theme="dark"] body {background:#0b1120;color:#e2e8f0;}       
        [data-theme="dark"] .step {border-color:rgba(255,255,255,.08);background:linear-gradient(135deg,#1e293b,#0f172a);}       
        [data-theme="dark"] .divider {background:linear-gradient(90deg,transparent,rgba(255,255,255,.15),transparent);}     
    </style>
</head>
<body>
    <main class="verify-container">
        <h1>Verify your email</h1>
        <p>We've sent a secure verification link to <strong>{{ auth()->user()->email }}</strong>. Click the link in that email to continue. This helps us protect your account and keep your health data safe.</p>

        @if ($status === 'verification-link-sent')
            <div class="status-chip">A fresh verification link has been sent.</div>
        @endif

        <div class="divider"></div>
        <div class="hint">Next After Verification</div>
        <div class="steps" aria-label="Upcoming quick steps after verifying">
            <div class="step"><div><strong>1. Welcome Tour</strong><br><small>See a 30-second overview of your dashboard features.</small></div></div>
            <div class="step"><div><strong>2. One-Time Vital Entry</strong><br><small>Enter baseline vitals (heart rate, BP, weight, height) once to personalize insights.</small></div></div>
            <div class="step"><div><strong>3. Personalized Dashboard</strong><br><small>Get directed to your live dashboard with your initial metrics displayed.</small></div></div>
        </div>

        <div class="divider"></div>
        <p style="margin-bottom:1rem;">
            Didn't get the email? You can request another secure link below.
        </p>
        <form method="POST" action="{{ route('verification.send') }}" class="inline">
            @csrf
            <button type="submit" class="resend-btn">Resend Verification Email</button>
        </form>
        <form method="POST" action="{{ route('logout') }}" class="inline" style="margin-left:1rem;">
            @csrf
            <button type="submit" class="logout-link">Log Out</button>
        </form>
    </main>
</body>
</html>
