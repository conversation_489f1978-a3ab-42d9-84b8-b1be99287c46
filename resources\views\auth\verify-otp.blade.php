<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Verify Email – Healthcare Tracker</title>
  @vite(['resources/css/app.css','resources/js/app.js'])
  <style>
    .otp-grid { display:grid; grid-template-columns: repeat(6, minmax(0, 1fr)); gap:.5rem; }
    .otp-input {
      text-align:center; font-size:1.25rem; padding:0.85rem 0; height:3.1rem;
      border:1px solid var(--color-border); border-radius:var(--radius-md);
      background:var(--color-surface-alt); color:var(--text,#0f172a);
      outline:none; transition:border-color .2s, box-shadow .2s;
    }
    .otp-input:focus { border-color: var(--color-primary); box-shadow:0 0 0 3px color-mix(in srgb, var(--color-primary) 20%, transparent); }
    .dark .otp-input { background: var(--color-surface-dark, #0f172a); color:#e2e8f0; border-color:#334155; }
  </style>
  </head>
<body class="antialiased font-sans bg-base text-base-text dark:bg-dark-base dark:text-dark-text auth-wrapper">
  <div class="auth-gradient" aria-hidden="true"></div>
  <canvas id="bg-canvas" aria-hidden="true"></canvas>
  <header class="site-header" style="background:transparent;border:0;box-shadow:none;">
    <nav class="nav">
      <div class="nav__logo">💚 <span>Healthcare<span class="accent">Tracker</span></span></div>
      <a href="/" class="btn btn--ghost" style="margin-left:auto">← Home</a>
    </nav>
  </header>

  <main class="section auth-main" data-animate="fade-up">
    <div style="max-width:440px;margin:0 auto; position:relative;">
      <h1 class="section__title" style="text-align:center;font-size:clamp(2rem,4vw,2.2rem);">Verify your email</h1>
      <p class="section__subtitle" style="text-align:center;">We've sent a 6-digit code to your email. Enter it below to verify your account.</p>

      @if (session('status'))
        <div class="onb-success" role="status" style="margin-top:1rem;">{{ session('status') }}</div>
      @endif

      @if ($errors->any())
        <div class="onb-error" role="alert" style="margin-top:1rem;">
          <strong>We found some issues:</strong>
          <ul>
            @foreach ($errors->all() as $error)
              <li>{{ $error }}</li>
            @endforeach
          </ul>
        </div>
      @endif

      <form method="POST" action="{{ route('verification.otp.verify') }}" class="feature-card auth-card" style="margin-top:1.25rem;">
        @csrf
        <label style="display:flex;flex-direction:column;gap:.5rem;font-size:.85rem;">
          <span>Verification code</span>
          <div class="otp-grid">
            <input inputmode="numeric" maxlength="1" class="otp-input" name="c1" aria-label="Digit 1" />
            <input inputmode="numeric" maxlength="1" class="otp-input" name="c2" aria-label="Digit 2" />
            <input inputmode="numeric" maxlength="1" class="otp-input" name="c3" aria-label="Digit 3" />
            <input inputmode="numeric" maxlength="1" class="otp-input" name="c4" aria-label="Digit 4" />
            <input inputmode="numeric" maxlength="1" class="otp-input" name="c5" aria-label="Digit 5" />
            <input inputmode="numeric" maxlength="1" class="otp-input" name="c6" aria-label="Digit 6" />
          </div>
        </label>
        <input type="hidden" name="code" id="code" />
        <button type="submit" class="btn btn--primary-solid btn--lg btn--center" style="margin-top:1rem;">Verify</button>
      </form>

      <form method="POST" action="{{ route('verification.otp.resend') }}" class="mt-4" style="text-align:center;margin-top:1rem;">
        @csrf
        <button type="submit" class="link" style="font-size:.9rem;">Resend code</button>
      </form>
    </div>
  </main>

  <script>
    (function(){
      const inputs = document.querySelectorAll('.otp-input');
      const hidden = document.getElementById('code');
      inputs.forEach((el, idx) => {
        el.addEventListener('input', () => {
          el.value = el.value.replace(/[^0-9]/g,'');
          if (el.value && idx < inputs.length - 1) inputs[idx+1].focus();
          hidden.value = Array.from(inputs).map(i=>i.value||'').join('');
        });
        el.addEventListener('keydown', (e) => {
          if (e.key === 'Backspace' && !el.value && idx>0) {
            inputs[idx-1].focus();
          }
        });
      });
    })();
  </script>
</body>
</html>
