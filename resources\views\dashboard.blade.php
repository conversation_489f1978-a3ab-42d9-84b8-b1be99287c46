<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>Dashboard – Healthcare Tracker</title>
    @vite(['resources/css/app.css','resources/js/app.js'])
    <style>
        /* Dark-mode friendly toggle for time range */
        #range-group { display:flex; gap:.25rem; border-radius:999px; padding:.15rem; background:#fff; border:1px solid rgba(0,0,0,.08); }
        .dark #range-group { background:#0f172a; border-color:#334155; }
        #range-group .btn { border:none; padding:.35rem .6rem; border-radius:999px; font-size:.8rem; }
        /* Unselected (outline) looks muted; selected has a subtle pill */
        #range-group .btn.btn--outline { background:transparent; opacity:.9; }
        #range-group .btn:not(.btn--outline) { background:rgba(0,0,0,.08); }
        .dark #range-group .btn:not(.btn--outline) { background:#111827; color:#e5e7eb; }
        .dark #range-group .btn.btn--outline { color:#cbd5e1; }
    </style>
</head>
<body class="antialiased font-sans bg-base text-base-text dark:bg-dark-base dark:text-dark-text">
    <canvas id="bg-canvas" aria-hidden="true"></canvas>
    @include('partials.navbar')

    <main class="section" id="main" data-animate="fade-up">
        @if (!empty($showDailyPrompt))
            <div id="daily-prompt" style="position:fixed; inset:0; display:flex; align-items:center; justify-content:center; background:rgba(0,0,0,.35); z-index:80;">
                <div class="feature-card" style="max-width:520px; width:92%;">
                    <h3 style="margin-top:0;">Time to log today’s vitals</h3>
                    <p class="op-80">It’s after noon Tunisia time and we haven’t got today’s vitals yet. This helps keep your dashboard accurate.</p>
                    <div style="display:flex; gap:.5rem; justify-content:flex-end; margin-top:.75rem;">
                        <button class="btn btn--outline" onclick="document.getElementById('daily-prompt').remove()">Later</button>
                        <a class="btn btn--primary-solid" href="{{ route('onboarding.show', ['daily' => 1]) }}">Add now</a>
                    </div>
                </div>
            </div>
        @endif
        @if (request()->query('setup') === 'done')
            <style>
                .setup-anim{position:fixed;top:88px;left:50%;transform:translateX(-50%);
                    background:var(--surface,#fff);border:1px solid rgba(0,0,0,.08);border-radius:14px;
                    padding:.65rem .9rem;display:flex;align-items:center;gap:.55rem;box-shadow:0 10px 28px -12px rgba(0,0,0,.35);
                    animation:setup-slidefade 3.2s cubic-bezier(.4,0,.2,1) forwards;z-index:60;pointer-events:none}
                .dark .setup-anim{background:#111827;border-color:rgba(255,255,255,.08);box-shadow:0 10px 28px -12px rgba(0,0,0,.7)}
                @keyframes setup-slidefade{0%{opacity:0;transform:translate(-50%,-10px)}12%{opacity:1;transform:translate(-50%,0)}82%{opacity:1}100%{opacity:0;transform:translate(-50%,-6px)}}
                .setup-anim__icon{width:28px;height:28px;color:#16a34a}
                .dark .setup-anim__icon{color:#22c55e}
                .setup-anim__icon path{stroke-dasharray:90;stroke-dashoffset:90;animation:setup-draw 1.1s ease .25s forwards}
                @keyframes setup-draw{to{stroke-dashoffset:0}}
                .setup-anim__title{margin:0;font-weight:700;font-size:.95rem}
                .setup-anim__sub{margin:0;font-size:.76rem;opacity:.75}
                .setup-anim__glow{position:absolute;inset:-2px;filter:blur(12px);border-radius:inherit;background:conic-gradient(from 0deg,transparent,rgba(34,197,94,.18),transparent 35%);animation:setup-spin 3s linear infinite;opacity:.6;pointer-events:none}
                @keyframes setup-spin{to{transform:rotate(1turn)}}
            </style>
            <div class="setup-anim" aria-live="polite" aria-atomic="true">
                <div class="setup-anim__glow" aria-hidden="true"></div>
                <svg class="setup-anim__icon" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                    <circle cx="24" cy="24" r="20" stroke="currentColor" stroke-width="3" opacity=".25" />
                    <path d="M14 24.5l7 7L34 18" stroke="currentColor" stroke-width="3.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <div>
                    <p class="setup-anim__title">You're all set!</p>
                    <p class="setup-anim__sub">Your personalized health dashboard is ready.</p>
                </div>
            </div>
        @endif

        <div style="display:grid;grid-template-columns:260px 1fr; gap:1.25rem; max-width:1200px; margin:0 auto; align-items:start;">
            <!-- Sidebar -->
            <aside class="feature-card" style="position:sticky; top:90px;">
                <h3 style="margin-top:0;">Quick Links</h3>
                <ul class="check-list">
                    <li><a class="link" href="#profile">Profile</a></li>
                    <li><a class="link" href="#vitals">Vitals</a></li>
                    <li><a class="link" href="#medications">Medications</a></li>
                    <li><a class="link" href="#alerts">Alerts</a></li>
                    <li><a class="link" href="#reports">Reports</a></li>
                </ul>
            </aside>

            <!-- Main area -->
            <section style="display:grid; gap:1.25rem;">
                <!-- Overview / Summary -->
                <div class="feature-card" data-animate="fade-up" style="display:flex; gap:1rem; align-items:center;">
                    <div id="summary-dot" data-color="{{ $summary['color'] ?? '#64748b' }}" style="width:10px; height:10px; border-radius:999px;"></div>
                    <div>
                        <div class="text-sm" style="font-weight:700;">Health Status: <span id="summary-status" data-color="{{ $summary['color'] ?? '#64748b' }}">{{ $summary['status'] ?? '—' }}</span></div>
                        <div class="text-2xs op-70">{{ $summary['note'] ?? '' }}</div>
                    </div>
                    <div class="ml-auto" style="display:flex; gap:.5rem; align-items:center; flex-wrap:wrap;">
                        <div id="range-group" role="group" aria-label="Time range">
                            <button class="btn" id="range-24h" data-range="24h">Live 24h</button>
                            <button class="btn btn--outline" id="range-7d" data-range="7d">7 days</button>
                        </div>
                        <a class="btn btn--outline" href="{{ route('dashboard.export.csv') }}" title="Export CSV">Export CSV</a>
                    </div>
                </div>

                <div id="profile" class="feature-card" data-animate="fade-up">
                    <div style="display:flex; gap:1rem; align-items:center;">
                        @php($fallback = 'https://api.dicebear.com/7.x/miniavs/svg?seed=' . urlencode(auth()->user()->name ?? 'U'))
                        <img alt="avatar" src="{{ auth()->user()->photo_url ?? $fallback }}" style="width:64px;height:64px;border-radius:50%;" />
                        <div>
                            <h3 style="margin:.2rem 0;">{{ auth()->user()->name ?? 'User' }}</h3>
                            <p class="text-sm op-70" style="margin:0;">{{ auth()->user()->email ?? '' }}</p>
                        </div>
                        <div class="ml-auto"><a class="btn btn--outline" href="{{ route('profile.show') }}">Edit Profile</a></div>
                    </div>
                </div>

                <div id="vitals" class="feature-card" data-animate="fade-up">
                    <h3 style="margin-top:0;">Vitals</h3>
                    
                    <div class="features__grid" style="grid-template-columns:repeat(auto-fit,minmax(240px,1fr))">
                        <!-- Heart Rate -->
                        <div class="onb-card" id="card-hr" style="display:flex; flex-direction:column; gap:.5rem;">
                            <div style="display:flex; align-items:baseline; gap:.5rem;">
                                <h3 style="margin:0;">Heart Rate</h3>
                                <span id="trend-hr" class="text-2xs" style="padding:.15rem .35rem; border-radius:6px; background:rgba(22,163,74,.1); color:#16a34a;">{{ ($trend['hr'] ?? 0) > 0 ? '+' : '' }}{{ $trend['hr'] ?? 0 }}%</span>
                                <span class="text-2xs op-60 ml-auto">bpm</span>
                            </div>
                            <div id="value-hr" style="font-size:1.75rem; font-weight:800;">{{ ($current?->heart_rate) ? number_format($current->heart_rate) : '—' }}</div>
                            <svg id="spark-hr" viewBox="0 0 120 36" width="100%" height="36" preserveAspectRatio="none" aria-hidden="true">
                                <path id="path-hr" d="{{ $paths['hr24'] ?? '' }}" fill="none" stroke="currentColor" stroke-width="2" style="opacity:.9; color:#ef4444"></path>
                            </svg>
                        </div>

                        <!-- Blood Pressure -->
                        <div class="onb-card" id="card-bp" style="display:flex; flex-direction:column; gap:.5rem;">
                            <div style="display:flex; align-items:baseline; gap:.5rem;">
                                <h3 style="margin:0;">Blood Pressure</h3>
                                <span id="trend-bp" class="text-2xs" style="padding:.15rem .35rem; border-radius:6px; background:rgba(59,130,246,.1); color:#3b82f6;">{{ ($trend['bp'] ?? 0) > 0 ? '+' : '' }}{{ $trend['bp'] ?? 0 }}%</span>
                                <span class="text-2xs op-60 ml-auto">SYS/DIA</span>
                            </div>
                            <div id="value-bp" style="font-size:1.75rem; font-weight:800;">{{ ($current?->blood_pressure_systolic && $current?->blood_pressure_diastolic) ? ($current->blood_pressure_systolic.'/'.$current->blood_pressure_diastolic) : '—' }}</div>
                            <svg id="spark-bp" viewBox="0 0 120 36" width="100%" height="36" preserveAspectRatio="none" aria-hidden="true">
                                <path id="path-bp" d="{{ $paths['bp24'] ?? '' }}" fill="none" stroke="currentColor" stroke-width="2" style="opacity:.9; color:#3b82f6"></path>
                            </svg>
                        </div>

                        <!-- Temperature -->
                        <div class="onb-card" id="card-temp" style="display:flex; flex-direction:column; gap:.5rem;">
                            <div style="display:flex; align-items:baseline; gap:.5rem;">
                                <h3 style="margin:0;">Temperature</h3>
                                <span id="trend-temp" class="text-2xs" style="padding:.15rem .35rem; border-radius:6px; background:rgba(245,158,11,.1); color:#f59e0b;">{{ ($trend['temp'] ?? 0) > 0 ? '+' : '' }}{{ $trend['temp'] ?? 0 }}%</span>
                                <span class="text-2xs op-60 ml-auto">°C</span>
                            </div>
                            <div id="value-temp" style="font-size:1.75rem; font-weight:800;">{{ ($current?->temperature) ? number_format($current->temperature, 1) : '—' }}</div>
                            <svg id="spark-temp" viewBox="0 0 120 36" width="100%" height="36" preserveAspectRatio="none" aria-hidden="true">
                                <path id="path-temp" d="{{ $paths['temp24'] ?? '' }}" fill="none" stroke="currentColor" stroke-width="2" style="opacity:.9; color:#f59e0b"></path>
                            </svg>
                        </div>

                        <!-- SpO₂ -->
                        <div class="onb-card" id="card-spo2" style="display:flex; flex-direction:column; gap:.5rem;">
                            <div style="display:flex; align-items:baseline; gap:.5rem;">
                                <h3 style="margin:0;">Oxygen</h3>
                                <span id="trend-spo2" class="text-2xs" style="padding:.15rem .35rem; border-radius:6px; background:rgba(34,197,94,.1); color:#22c55e;">{{ ($trend['spo2'] ?? 0) > 0 ? '+' : '' }}{{ $trend['spo2'] ?? 0 }}%</span>
                                <span class="text-2xs op-60 ml-auto">SpO₂ %</span>
                            </div>
                            <div id="value-spo2" style="font-size:1.75rem; font-weight:800;">{{ ($current?->spo2_level) ? number_format($current->spo2_level) : '—' }}</div>
                            <svg id="spark-spo2" viewBox="0 0 120 36" width="100%" height="36" preserveAspectRatio="none" aria-hidden="true">
                                <path id="path-spo2" d="{{ $paths['spo224'] ?? '' }}" fill="none" stroke="currentColor" stroke-width="2" style="opacity:.9; color:#22c55e"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div id="trends" class="feature-card" data-animate="fade-up">
                    <h3 style="margin-top:0;">Trends (Last 7 days)</h3>
                    @if(($__count = count($series7d ?? [])) > 0)
                        @php($__last = ($series7d[$__count - 1] ?? null))
                        <div class="text-sm" style="display:flex; gap:1rem; flex-wrap:wrap;">
                            <div>Days tracked: <strong>{{ $__count }}</strong></div>
                            <div>Avg HR: <strong>{{ isset($__last['hr']) ? number_format($__last['hr']) : '—' }} bpm</strong></div>
                            <div>Avg BP: <strong>{{ (isset($__last['sys']) && isset($__last['dia'])) ? number_format((($__last['sys']+$__last['dia'])/2)) : '—' }}</strong></div>
                            <div>Avg Temp: <strong>{{ isset($__last['temp']) ? number_format($__last['temp'],1) : '—' }} °C</strong></div>
                            <div>Avg SpO₂: <strong>{{ isset($__last['spo2']) ? number_format($__last['spo2']) : '—' }}%</strong></div>
                        </div>
                    @else
                        <p class="text-sm op-70">No 7-day data yet.</p>
                    @endif
                </div>

                <div id="actions" class="feature-card" data-animate="fade-up" style="display:flex; gap:.75rem; flex-wrap:wrap;">
                    <a class="btn btn--primary-solid" href="#">Call Doctor</a>
                    <a class="btn btn--outline" href="#">Emergency Contact</a>
                </div>
            </section>
        </div>
    </main>

    <!-- All setup-related modals removed to keep quick setup inline only. -->
    <div id="dash-data"
         data-hr24="{{ $paths['hr24'] ?? '' }}"
         data-bp24="{{ $paths['bp24'] ?? '' }}"
         data-temp24="{{ $paths['temp24'] ?? '' }}"
         data-spo224="{{ $paths['spo224'] ?? '' }}"
         data-series7d='{{ json_encode($series7d ?? []) }}'
         data-trend='{{ json_encode($trend ?? []) }}'
         style="display:none"></div>
    <script>
        (function(){
            const fmt = (v, d=0) => (v==null || isNaN(v)) ? '—' : Number(v).toFixed(d);
            const sel = id => document.getElementById(id);
            const computePath = (values, width=120, height=36, pad=2) => {
                const n = values.length;
                if(n <= 1) return '';
                let min = Math.min(...values), max = Math.max(...values);
                if(min === max){ min -= 1; max += 1; }
                const innerW = width - 2*pad, innerH = height - 2*pad;
                const stepX = innerW / Math.max(1, n-1);
                let d = '';
                for(let i=0;i<n;i++){
                    const x = pad + i*stepX;
                    const norm = (values[i]-min)/(max-min);
                    const y = pad + innerH - norm*innerH;
                    d += (i===0? 'M':'L') + x.toFixed(2)+','+y.toFixed(2)+' ';
                }
                return d.trim();
            };
            const pct = (prev, curr) => (!prev || !curr) ? 0 : Math.round(((curr - prev) / prev) * 1000)/10;
            const setTrend = (el, val) => {
                const v = Number(val)||0;
                el.textContent = (v>0?'+':'') + v + '%';
                // Color logic
                let color = '#64748b', bg = 'rgba(100,116,139,.12)';
                if(el.id.endsWith('hr')){ color = v>=0 ? '#16a34a' : '#dc2626'; bg = `rgba(${v>=0? '22,163,74':'220,38,38'},.1)`; }
                else if(el.id.endsWith('bp')){ color = v<=0 ? '#16a34a' : '#f97316'; bg = `rgba(${v<=0? '22,163,74':'249,115,22'},.1)`; }
                else if(el.id.endsWith('temp')){ color = v<=0 ? '#16a34a' : '#f59e0b'; bg = `rgba(${v<=0? '22,163,74':'245,158,11'},.1)`; }
                else if(el.id.endsWith('spo2')){ color = v>=0 ? '#22c55e' : '#ef4444'; bg = `rgba(${v>=0? '34,197,94':'239,68,68'},.1)`; }
                el.style.color = color; el.style.background = bg;
            };

            // Seed from server-rendered data via data-* attributes
            const meta = (document.getElementById('dash-data') || {}).dataset || {};
            const data24 = {
                hrPath: meta.hr24 || '',
                bpPath: meta.bp24 || '',
                tempPath: meta.temp24 || '',
                spo2Path: meta.spo224 || '',
            };
            let s7 = [];
            try { s7 = JSON.parse(meta.series7d || '[]'); } catch(e) { s7 = []; }
            const pathFrom7d = (key) => computePath((s7||[]).map(p => Number(p[key]||0)));

            // Apply initial trend colors
            let trend = {};
            try { trend = JSON.parse(meta.trend || '{}'); } catch(e) { trend = {}; }
            setTrend(sel('trend-hr'), Number(trend.hr || 0));
            setTrend(sel('trend-bp'), Number(trend.bp || 0));
            setTrend(sel('trend-temp'), Number(trend.temp || 0));
            setTrend(sel('trend-spo2'), Number(trend.spo2 || 0));

            // Range toggle
            let currentRange = '24h';
            const applyRange = (range) => {
                currentRange = range;
                const a = sel('range-24h'), b = sel('range-7d');
                if(range==='24h'){ a.classList.remove('btn--outline'); b.classList.add('btn--outline'); }
                else { b.classList.remove('btn--outline'); a.classList.add('btn--outline'); }

                if(range==='24h'){
                    sel('path-hr').setAttribute('d', data24.hrPath||'');
                    sel('path-bp').setAttribute('d', data24.bpPath||'');
                    sel('path-temp').setAttribute('d', data24.tempPath||'');
                    sel('path-spo2').setAttribute('d', data24.spo2Path||'');
                } else {
                    sel('path-hr').setAttribute('d', pathFrom7d('hr'));
                    // For BP, average sys/dia
                    const bp7 = (s7||[]).map(p => ((Number(p['sys']||0)+Number(p['dia']||0))/2));
                    sel('path-bp').setAttribute('d', computePath(bp7));
                    sel('path-temp').setAttribute('d', pathFrom7d('temp'));
                    sel('path-spo2').setAttribute('d', pathFrom7d('spo2'));
                }
                ['spark-hr','spark-bp','spark-temp','spark-spo2'].forEach(id=>{
                    const el = sel(id); if(!el) return; el.style.opacity = .7; setTimeout(()=>{ el.style.opacity = 1; }, 200);
                });
            };
            sel('range-24h')?.addEventListener('click', ()=>applyRange('24h'));
            sel('range-7d')?.addEventListener('click', ()=>applyRange('7d'));
            applyRange('24h');

            // Live polling
            const feedUrl = "{{ route('vitals.feed') }}";
            const poll = async () => {
                try {
                    const res = await fetch(feedUrl);
                    const j = await res.json();
                    if(!j || !j.ok) return;
                    const { current, series24h } = j;
                    // Update numbers
                    if(current){
                        if('hr' in current) sel('value-hr').textContent = fmt(current.hr);
                        if('sys' in current && 'dia' in current){
                            const sys = current.sys, dia = current.dia;
                            sel('value-bp').textContent = (sys && dia) ? (sys + '/' + dia) : '—';
                        }
                        if('temp' in current) sel('value-temp').textContent = fmt(current.temp, 1);
                        if('spo2' in current) sel('value-spo2').textContent = fmt(current.spo2);
                    }
                    // Update spark paths from series
                    if(Array.isArray(series24h) && series24h.length){
                        const hrVals = series24h.map(p=> Number(p.hr||0));
                        const bpVals = series24h.map(p=> ((Number(p.sys||0)+Number(p.dia||0))/2));
                        const tempVals = series24h.map(p=> Number(p.temp||0));
                        const spo2Vals = series24h.map(p=> Number(p.spo2||0));
                        data24.hrPath = computePath(hrVals);
                        data24.bpPath = computePath(bpVals);
                        data24.tempPath = computePath(tempVals);
                        data24.spo2Path = computePath(spo2Vals);
                        if(currentRange==='24h'){
                            sel('path-hr').setAttribute('d', data24.hrPath);
                            sel('path-bp').setAttribute('d', data24.bpPath);
                            sel('path-temp').setAttribute('d', data24.tempPath);
                            sel('path-spo2').setAttribute('d', data24.spo2Path);
                        }
                        // Recompute trends from last two points
                        if(series24h.length>=2){
                            const prev = series24h[series24h.length-2];
                            const curr = series24h[series24h.length-1];
                            const tHr = pct(prev.hr||0, curr.hr||0);
                            const tBp = pct(((prev.sys||0)+(prev.dia||0))/2, ((curr.sys||0)+(curr.dia||0))/2);
                            const tTemp = pct(prev.temp||0, curr.temp||0);
                            const tSp = pct(prev.spo2||0, curr.spo2||0);
                            setTrend(sel('trend-hr'), tHr);
                            setTrend(sel('trend-bp'), tBp);
                            setTrend(sel('trend-temp'), tTemp);
                            setTrend(sel('trend-spo2'), tSp);
                        }
                        ['spark-hr','spark-bp','spark-temp','spark-spo2'].forEach(id=>{
                            const el = sel(id); if(!el) return; el.style.opacity = .7; setTimeout(()=>{ el.style.opacity = 1; }, 200);
                        });
                    }
                } catch(e){ /* silent */ }
            };
            // Set summary colors
            (function(){
                const dot = document.getElementById('summary-dot');
                const status = document.getElementById('summary-status');
                if(dot && dot.dataset.color) dot.style.background = dot.dataset.color;
                if(status && status.dataset.color) status.style.color = status.dataset.color;
            })();
            setInterval(poll, 15000);
        })();
    </script>
</body>
</html>
