<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>Setup your profile – Healthcare Tracker</title>
    <meta name="description" content="Guided onboarding to personalize your health dashboard." />
    @vite(['resources/css/app.css','resources/js/onboarding.js'])
    <style>
        /* Scoped form styling for onboarding */
        .onb-card h3 { margin:.2rem 0 1rem; font-size:1.05rem; font-weight:700; }
        .form-grid { display:grid; grid-template-columns:repeat(auto-fit, minmax(240px,1fr)); gap:14px 16px; align-items:start; }
        .form-grid .full { grid-column:1 / -1; }
        .form-grid label { display:flex; flex-direction:column; gap:.4rem; }
        .form-grid label > span { font-size:.8rem; font-weight:600; letter-spacing:.25px; color:var(--muted,#64748b); }
    .form-grid input[type="number"],
    .form-grid input[type="text"],
    .form-grid textarea { width:100%; max-width:100%; box-sizing:border-box; padding:.7rem .85rem; border:1px solid rgba(0,0,0,.12); border-radius:.65rem; background:#fff; color:inherit; font:inherit; box-shadow:0 0 0 0 rgba(37,99,235,0); transition:border-color .15s ease, box-shadow .15s ease; }
    .form-grid textarea { min-height:96px; max-height:180px; resize:vertical; overflow:auto; }
        .dark .form-grid input[type="number"],
        .dark .form-grid input[type="text"],
        .dark .form-grid textarea { background:#0f172a; border-color:#1e293b; color:#e2e8f0; }
        .form-grid input::placeholder,
        .form-grid textarea::placeholder { color:rgba(100,116,139,.75); }
        .dark .form-grid input::placeholder,
        .dark .form-grid textarea::placeholder { color:rgba(148,163,184,.7); }
        .form-grid input:focus,
        .form-grid textarea:focus { outline:none; border-color:#2563eb66; box-shadow:0 0 0 3px #2563eb22; }
        .input-group { display:flex; gap:.5rem; align-items:center; }
        .input-group > input { flex:1; }
        .input-group > span { color:var(--muted,#64748b); font-weight:600; }
        .onb-actions { display:flex; gap:.6rem; align-items:center; margin-top:1rem; }
        @media (max-width: 520px) { .onb-actions { flex-wrap:wrap; } }
        .field-error{ color:#dc2626; font-size:.8rem; margin-top:.25rem; }
        .dark .field-error{ color:#f87171; }
    </style>
</head>
<body class="antialiased font-sans bg-base text-base-text dark:bg-dark-base dark:text-dark-text">
<header class="site-header" style="position:sticky;top:0;z-index:50;background:transparent;backdrop-filter:saturate(160%) blur(6px);">
    <nav class="nav" aria-label="Top navigation" style="display:flex;align-items:center;gap:1rem;">
        <div class="nav__logo">💚 <span>Healthcare<span class="accent">Tracker</span></span></div>
        <ul class="nav__menu" style="display:flex;gap:.9rem;margin-left:auto;align-items:center;">
            <li><a class="link" href="{{ route('dashboard') }}">Dashboard</a></li>
            <li><a class="link" href="#vitals">Vitals</a></li>
            <li><a class="link" href="#trends">Trends</a></li>
            <li>
                <button id="themeToggle" class="btn btn--ghost" aria-pressed="false" aria-label="Toggle dark mode">🌙</button>
            </li>
            <li>
                <button class="icon-btn" aria-label="Notifications">
                    <span class="i-bell" aria-hidden="true">🔔</span>
                </button>
            </li>
            <li>
                <div class="avatar-menu">
                    <button class="avatar" aria-haspopup="menu" aria-expanded="false" aria-label="Open user menu">
                        @php($fallback = 'https://api.dicebear.com/7.x/miniavs/svg?seed=' . urlencode(Auth::user()->name ?? 'User'))
                        <img src="{{ Auth::user()->photo_url ?? $fallback }}" alt="Profile" />
                    </button>
                    <ul class="menu" role="menu">
                        <li role="menuitem"><a class="link" href="{{ route('profile.show') }}">Profile</a></li>
                        <li role="menuitem"><a class="link" href="{{ route('settings.index') }}">Settings</a></li>
                        <li role="menuitem"><form method="POST" action="{{ route('logout') }}">@csrf<button type="submit" class="link-btn">Logout</button></form></li>
                    </ul>
                </div>
            </li>
        </ul>
    </nav>
</header>

<main class="section" id="onboardingRoot" data-animate="fade-up">
    <div class="onb-container">
        <div class="onb-progress">
            <div class="onb-progress__bar" id="progressBar" style="width: 16.66%"></div>
            <ul class="onb-steps" aria-label="Progress" role="list">
                <li class="onb-step onb-step--active" data-step="1">1</li>
                <li class="onb-step" data-step="2">2</li>
                <li class="onb-step" data-step="3">3</li>
            </ul>
        </div>

    <form id="onboardingForm" class="feature-card auth-card onb-card" method="POST" action="{{ route('onboarding.store') }}" novalidate>
            @csrf
            @if ($errors->any())
                <div class="onb-error">
                    <strong>We found some issues:</strong>
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <!-- Step 1: Welcome / Daily entry -->
            <section class="onb-step-pane onb-step-pane--active" data-step="1" aria-label="Welcome">
                @if(!empty($isDailyFlow))
                    <h1>Add today’s vitals</h1>
                    <p>Log your latest measurements to keep your dashboard up to date. You can update these later today if needed.</p>
                    <p class="text-sm" style="color:var(--muted,#64748b); margin-top:.25rem;">
                        These are your latest values
                        @if(!empty($latestVital) && !empty($latestVital->recorded_at))
                            from {{ $latestVital->recorded_at->copy()->setTimezone('Africa/Tunis')->format('M j, Y H:i') }} Africa/Tunis
                        @endif
                        — review and adjust if needed.
                    </p>
                @else
                    <h1>Welcome to Healthcare Tracker</h1>
                    <p>We’ll help you set up a personalized health dashboard in a few quick steps.</p>
                @endif
                <div class="onb-actions">
                    <button type="button" class="btn btn--primary-solid" data-next>Let’s start</button>
                    <button type="submit" form="onboardingSkipForm" class="btn btn--ghost" style="margin-left:.5rem;">Skip for now</button>
                </div>
            </section>

            <!-- Step 2: Vitals Only -->
            <section class="onb-step-pane" data-step="2" aria-label="Vitals">
                <h3>Baseline vitals</h3>
                <div class="form-grid">
                    <label><span>Height (cm)</span>
                        <input type="number" name="height_cm" step="0.1" min="40" max="260"
                               value="{{ old('height_cm', !empty($isDailyFlow) ? ($patient->height_cm ?? '') : '') }}" />
                    </label>
                    <label><span>Weight (kg)</span>
                        <input type="number" name="weight" step="0.1" min="2" max="400"
                               value="{{ old('weight', !empty($isDailyFlow) ? ($latestVital->weight ?? '') : '') }}" />
                    </label>
                    <label><span>Blood Pressure</span>
                        <div class="input-group">
                            <input type="number" name="blood_pressure_systolic" min="60" max="250"
                                   placeholder="SYS"
                                   value="{{ old('blood_pressure_systolic', !empty($isDailyFlow) ? ($latestVital->blood_pressure_systolic ?? '') : '') }}" />
                            <span>/</span>
                            <input type="number" name="blood_pressure_diastolic" min="40" max="150"
                                   placeholder="DIA"
                                   value="{{ old('blood_pressure_diastolic', !empty($isDailyFlow) ? ($latestVital->blood_pressure_diastolic ?? '') : '') }}" />
                        </div>
                    </label>
                    <label><span>Heart Rate (bpm)</span>
                        <input type="number" name="heart_rate" min="20" max="250"
                               value="{{ old('heart_rate', !empty($isDailyFlow) ? ($latestVital->heart_rate ?? '') : '') }}" />
                    </label>
                    <label><span>Glucose (mg/dL)</span>
                        <input type="number" name="glucose_level" min="20" max="600"
                               value="{{ old('glucose_level', !empty($isDailyFlow) ? ($latestVital->glucose_level ?? '') : '') }}" />
                    </label>
                    <label><span>SpO₂ (%)</span>
                        <input type="number" name="spo2_level" min="50" max="100"
                               value="{{ old('spo2_level', !empty($isDailyFlow) ? ($latestVital->spo2_level ?? '') : '') }}" />
                    </label>
                    <label><span>Temperature (°C)</span>
                        <input type="number" name="temperature" step="0.1" min="30" max="45"
                               value="{{ old('temperature', !empty($isDailyFlow) ? ($latestVital->temperature ?? '') : '') }}" />
                    </label>
                </div>
                <div class="onb-actions">
                    <button type="button" class="btn" data-prev>Back</button>
                    <button type="button" class="btn btn--primary-solid" data-next>Continue</button>
                </div>
            </section>

            <!-- Step 3: Insurance & Health History (optional) -->
            <section class="onb-step-pane" data-step="3" aria-label="Insurance & Health History (optional)">
                <h3>Insurance & Health History <span class="text-xs" style="font-weight:500;color:var(--muted,#64748b);">(optional)</span></h3>
                <p class="text-sm" style="margin-top:-.25rem;color:var(--muted,#64748b);">These fields are optional. You can always add them later from <a href="{{ route('profile.show') }}" class="link">Profile</a>.</p>
                <div class="form-grid">
                    <label><span>Insurance Provider <em style="font-size:.8em;color:var(--muted,#64748b);">(optional)</em></span><input type="text" name="insurance_provider" maxlength="255" placeholder="e.g., Blue Cross" /></label>
                    <label><span>Policy Number <em style="font-size:.8em;color:var(--muted,#64748b);">(optional)</em></span><input type="text" name="policy_number" maxlength="100" placeholder="e.g., ABC-123456" /></label>
                    <label class="full"><span>Health History Summary <em style="font-size:.8em;color:var(--muted,#64748b);">(optional)</em></span><textarea name="health_history" rows="3" placeholder="e.g., Asthma since 2010; previous surgery in 2022."></textarea></label>
                </div>
                <div class="onb-actions">
                    <button type="button" class="btn" data-prev>Back</button>
                    <button type="submit" class="btn btn--primary-solid" id="finishBtn">Save & Finish</button>
                </div>
            </section>
        </form>


        <!-- Celebration -->
        <div id="confetti" class="confetti" aria-hidden="true"></div>
    </div>
    <!-- Standalone skip form to avoid nesting inside the main form -->
    <form id="onboardingSkipForm" method="POST" action="{{ route('onboarding.skip') }}" class="hidden">@csrf</form>
</main>
<script>
    // Progressive enhancement: set progress bar width via JS
    document.addEventListener('DOMContentLoaded', function(){
         const pb = document.getElementById('progressBar');
         if(pb) pb.style.transition = 'width .5s cubic-bezier(.4,0,.2,1)';
    });

    // No popup for setup. "Skip for now" posts directly; wizard runs inline within steps.
</script>
</body>
</html>
