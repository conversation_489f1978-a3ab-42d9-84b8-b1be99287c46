@extends('layouts.app')

@section('content')
<div class="container mx-auto max-w-3xl p-4">
  <h1 class="text-2xl font-semibold mb-4">Onboarding</h1>
  <div class="mb-6">
    <div class="w-full bg-gray-200 rounded h-2 overflow-hidden">
      <div class="bg-blue-600 h-2" style="width: {{ (int)(($step/6)*100) }}%"></div>
    </div>
    <p class="text-sm text-gray-600 mt-2">Step {{ $step }} of 6</p>
  </div>

  @if($errors->any())
    <div class="p-3 mb-4 text-red-900 bg-red-100 rounded">
      <ul class="list-disc pl-5">
        @foreach($errors->all() as $e)
          <li>{{ $e }}</li>
        @endforeach
      </ul>
    </div>
  @endif

  <form method="POST" action="{{ route('onboarding.step.post', ['step' => $step]) }}">
    @csrf

    @if($step === 1)
      <div class="space-y-4">
        <p class="text-gray-700">Welcome! We’ll guide you through a few quick steps to personalize your experience.</p>
      </div>
    @elseif($step === 2)
      <div class="space-y-4">
        <p class="text-gray-700">Account is set. Click Continue to proceed.</p>
      </div>
    @elseif($step === 3)
      <div class="space-y-4">
        <label class="block">
          <span class="text-gray-700">Name</span>
          <input name="name" type="text" value="{{ old('name', $session['profile']['name'] ?? auth()->user()->name) }}" class="mt-1 block w-full border rounded p-2" required />
        </label>
        <label class="block">
          <span class="text-gray-700">Email</span>
          <input name="email" type="email" value="{{ old('email', $session['profile']['email'] ?? auth()->user()->email) }}" class="mt-1 block w-full border rounded p-2" required />
        </label>
        <label class="block">
          <span class="text-gray-700">Date of Birth</span>
          <input name="date_of_birth" type="date" value="{{ old('date_of_birth', $session['profile']['date_of_birth'] ?? '') }}" class="mt-1 block w-full border rounded p-2" />
        </label>
        <label class="block">
          <span class="text-gray-700">Gender</span>
          <select name="gender" class="mt-1 block w-full border rounded p-2">
            <option value="">Select</option>
            @foreach(['male'=>'Male','female'=>'Female','other'=>'Other'] as $k=>$label)
              <option value="{{ $k }}" @selected(old('gender', $session['profile']['gender'] ?? '') === $k)>{{ $label }}</option>
            @endforeach
          </select>
        </label>
      </div>
    @elseif($step === 4)
      @php
        $vitalIndex = $session['vital_index'] ?? 0;
        $vitalOrder = [
          ['key' => 'height_cm', 'label' => 'Height (cm)', 'type' => 'number', 'step' => '0.1'],
          ['key' => 'weight', 'label' => 'Weight (kg)', 'type' => 'number', 'step' => '0.1'],
          ['key' => 'blood_pressure_systolic', 'label' => 'Blood Pressure Systolic', 'type' => 'number', 'step' => '1'],
          ['key' => 'blood_pressure_diastolic', 'label' => 'Blood Pressure Diastolic', 'type' => 'number', 'step' => '1'],
          ['key' => 'heart_rate', 'label' => 'Heart Rate (bpm)', 'type' => 'number', 'step' => '1'],
          ['key' => 'glucose_level', 'label' => 'Glucose (mg/dL)', 'type' => 'number', 'step' => '1'],
          ['key' => 'spo2_level', 'label' => 'SpO2 (%)', 'type' => 'number', 'step' => '1'],
          ['key' => 'temperature', 'label' => 'Temperature (°C)', 'type' => 'number', 'step' => '0.1'],
        ];
        $currentVital = $vitalOrder[min($vitalIndex, count($vitalOrder)-1)];
        $vitals = $session['vitals'] ?? [];
        $progress = (int)(($vitalIndex / count($vitalOrder)) * 100);
      @endphp
      <div class="space-y-4">
        <div class="w-full bg-gray-200 rounded h-2 overflow-hidden">
          <div class="bg-green-600 h-2" style="width: {{ $progress }}%"></div>
        </div>
        <label class="block">
          <span class="text-gray-700">{{ $currentVital['label'] }}</span>
          <input name="{{ $currentVital['key'] }}" type="{{ $currentVital['type'] }}" step="{{ $currentVital['step'] }}" value="{{ old($currentVital['key'], $vitals[$currentVital['key']] ?? '') }}" class="mt-1 block w-full border rounded p-2" />
        </label>
        <div class="flex gap-2">
          <button type="submit" name="skip" value="1" class="px-4 py-2 border rounded">Skip</button>
        </div>
      </div>
    @elseif($step === 5)
      <div class="space-y-4">
        <label class="block">
          <span class="text-gray-700">Allergies</span>
          <textarea name="allergies" class="mt-1 block w-full border rounded p-2">{{ old('allergies', $session['medical']['allergies'] ?? '') }}</textarea>
        </label>
        <label class="block">
          <span class="text-gray-700">Medical conditions</span>
          <textarea name="medical_conditions" class="mt-1 block w-full border rounded p-2">{{ old('medical_conditions', $session['medical']['medical_conditions'] ?? '') }}</textarea>
        </label>
        <div>
          <p class="text-gray-700 mb-2">Medications (enter one per row as: Name | Dosage | Frequency)</p>
          <textarea name="medications[0][name]" placeholder="e.g., Metformin" class="mt-1 block w-full border rounded p-2 mb-2">{{ old('medications.0.name', '') }}</textarea>
          <input name="medications[0][dosage]" placeholder="Dosage" class="block w-full border rounded p-2 mb-2" />
          <input name="medications[0][frequency]" placeholder="Frequency" class="block w-full border rounded p-2" />
        </div>
      </div>
    @elseif($step === 6)
      <div class="space-y-4">
        <p class="text-gray-700">Preferences (optional)</p>
        <label class="inline-flex items-center gap-2">
          <input type="checkbox" name="preferences[reminders]" value="1" @checked(old('preferences.reminders', $session['prefs']['preferences']['reminders'] ?? false)) />
          <span>Enable reminders</span>
        </label>
      </div>
    @endif

    <div class="mt-6 flex items-center gap-2">
      @if($step > 1)
        <button type="submit" name="back" value="1" class="px-4 py-2 border rounded">Back</button>
      @endif
      <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded">{{ $step < 6 ? 'Continue' : 'Finish' }}</button>
      @if($step < 6)
        <a href="{{ route('onboarding.skip') }}" onclick="event.preventDefault(); document.getElementById('skipForm').submit();" class="ml-auto text-gray-600">Skip for now</a>
      @endif
    </div>
  </form>
  <form id="skipForm" method="POST" action="{{ route('onboarding.skip') }}" class="hidden">@csrf</form>
</div>
@endsection
