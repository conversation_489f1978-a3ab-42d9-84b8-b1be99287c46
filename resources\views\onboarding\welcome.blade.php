<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <title>Welcome & Initial Vitals</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    @vite(['resources/css/app.css','resources/js/app.js'])
    <style>
        body {background:radial-gradient(circle at 30% 20%,#dbeafe,#fff),linear-gradient(180deg,#fff,#f1f5f9);min-height:100vh;}
        [data-theme="dark"] body {background:radial-gradient(circle at 30% 20%,#0f172a,#020617);}        
        .layout {max-width:1180px;margin:0 auto;padding:2.5rem 1.25rem;display:grid;gap:2rem;grid-template-columns:repeat(auto-fit,minmax(340px,1fr));align-items:start;}
        .card {background:var(--surface,#fff);border-radius:1.25rem;padding:1.75rem 1.6rem;box-shadow:0 8px 28px -6px rgba(0,0,0,.15),0 0 0 1px rgba(255,255,255,.5) inset;position:relative;overflow:hidden;}
        .card h2 {margin:0 0 .75rem;font-size:1.2rem;}
        .guide-steps {display:grid;gap:.9rem;margin-top:1rem;}
        .g-step {display:flex;gap:.85rem;padding:.85rem 1rem;border:1px solid rgba(0,0,0,.08);border-radius:.9rem;background:linear-gradient(135deg,#f8fafc,#fff);position:relative;}
        .g-step:before {content:attr(data-num);font-size:.7rem;font-weight:600;letter-spacing:.5px;display:inline-flex;align-items:center;justify-content:center;width:1.4rem;height:1.4rem;border-radius:50%;background:linear-gradient(135deg,#2563eb,#1d4ed8);color:#fff;margin-top:.2rem;}
        .vital-form {display:grid;gap:1rem;margin-top:1rem;}
        .vf-row {display:flex;gap:1rem;}
        .vf-row .field {flex:1;}
        label {display:block;font-size:.75rem;text-transform:uppercase;font-weight:600;letter-spacing:.5px;margin-bottom:.4rem;color:#0f172a;}
        [data-theme="dark"] label {color:#e2e8f0;}
        input[type=number] {width:100%;padding:.75rem .85rem;border:1px solid rgba(0,0,0,.12);border-radius:.7rem;background:#fff;font:inherit;}
        input[type=number]:focus {outline:2px solid #2563eb33;border-color:#2563eb66;}
        [data-theme="dark"] input[type=number] {background:#0f172a;border-color:#1e293b;color:#e2e8f0;}
        .submit-btn {margin-top:.25rem;background:linear-gradient(135deg,#2563eb,#1d4ed8);color:#fff;padding:.9rem 1.25rem;border-radius:.8rem;font-weight:600;letter-spacing:.5px;display:inline-flex;align-items:center;gap:.6rem;box-shadow:0 6px 18px -4px #1d4ed888;}
        .submit-btn:hover {box-shadow:0 8px 26px -4px #1d4ed899;}
        .bmi-note {font-size:.65rem;opacity:.7;margin-top:.35rem;}
        .hero-title {font-size:1.85rem;line-height:1.15;margin:0 0 .5rem;font-weight:700;background:linear-gradient(90deg,#1e3a8a,#2563eb);-webkit-background-clip:text;color:transparent;}
        [data-theme="dark"] .hero-title {background:linear-gradient(90deg,#60a5fa,#38bdf8);}
        .welcome-box {position:relative;padding-bottom:.5rem;}
        .progress-preview {display:flex;gap:.5rem;margin-top:1.25rem;}
        .p-chip {font-size:.6rem;font-weight:600;letter-spacing:.5px;padding:.35rem .55rem;border-radius:.5rem;background:#eff6ff;color:#1e3a8a;}
        [data-theme="dark"] .p-chip {background:#1e293b;color:#93c5fd;}
        .one-time-banner {position:absolute;top:0;right:0;font-size:.6rem;font-weight:600;background:linear-gradient(135deg,#16a34a,#15803d);color:#fff;padding:.4rem .6rem;border-bottom-left-radius:.6rem;letter-spacing:.5px;}
        [data-theme="dark"] .card {background:#111827;box-shadow:0 0 0 1px rgba(255,255,255,.06),0 10px 32px -8px rgba(0,0,0,.75);}        
        [data-theme="dark"] .g-step {border-color:rgba(255,255,255,.08);background:linear-gradient(135deg,#1e293b,#0f172a);}        
    </style>
</head>
<body>
    @include('partials.navbar')
    <div class="layout">
        <section class="card welcome-box">
            <div class="one-time-banner">ONE-TIME</div>
            <h1 class="hero-title">Welcome aboard, {{ auth()->user()->name }}!</h1>
            <p style="margin:0 0 1rem;max-width:48ch;">You're just a few seconds away from a personalized health dashboard. We'll capture a single baseline snapshot of your vitals now—then you're done. This helps tailor trends, alerts, and goals immediately.</p>
            <div class="guide-steps" aria-label="Quick feature guide">
                <div class="g-step" data-num="1"><div><strong>Baseline Capture</strong><br><small>Your starting vitals inform safe ranges and adaptive visuals.</small></div></div>
                <div class="g-step" data-num="2"><div><strong>Smart Dashboard</strong><br><small>Heart + ECG modules reflect your relative zones from today forward.</small></div></div>
                <div class="g-step" data-num="3"><div><strong>Adaptive Insights</strong><br><small>Future updates will trend your improvements and alert deviations.</small></div></div>
            </div>
            <div class="progress-preview" aria-hidden="true">
                <span class="p-chip">STEP: BASELINE</span>
                <span class="p-chip">NEXT: DASHBOARD</span>
            </div>
        </section>

        <section class="card">
            <h2>Enter Initial Vitals</h2>
            <form method="POST" action="{{ route('onboarding.welcome.store') }}" class="vital-form">
                @csrf
                <div class="vf-row">
                    <div class="field">
                        <label for="heart_rate">Heart Rate (bpm)</label>
                        <input required type="number" id="heart_rate" name="heart_rate" min="30" max="220" placeholder="72" />
                    </div>
                    <div class="field">
                        <label for="blood_pressure_systolic">Systolic</label>
                        <input required type="number" id="blood_pressure_systolic" name="blood_pressure_systolic" min="70" max="250" placeholder="120" />
                    </div>
                    <div class="field">
                        <label for="blood_pressure_diastolic">Diastolic</label>
                        <input required type="number" id="blood_pressure_diastolic" name="blood_pressure_diastolic" min="40" max="150" placeholder="80" />
                    </div>
                </div>
                <div class="vf-row">
                    <div class="field">
                        <label for="weight">Weight (kg)</label>
                        <input type="number" step="0.1" id="weight" name="weight" min="1" max="500" placeholder="70" />
                    </div>
                    <div class="field">
                        <label for="temperature">Temperature (°C)</label>
                        <input type="number" step="0.1" id="temperature" name="temperature" min="30" max="45" placeholder="36.6" />
                    </div>
                </div>
                <p class="bmi-note">Provide what you have now; you can add more vitals later.</p>
                <button type="submit" class="submit-btn">Save & Continue →</button>
            </form>
        </section>
    </div>
</body>
</html>
