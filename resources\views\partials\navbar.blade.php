<header class="site-header">
    <nav class="nav">
        <div class="nav__logo">💚 <span>Healthcare<span class="accent">Tracker</span></span></div>
        <ul class="nav__menu" style="margin-left:auto;">
            @php($isAdmin = auth()->check() && auth()->user()->role === 'admin')
            @php($routeName = request()->route()?->getName())
            @php($res = request()->route('resource'))
            @if($isAdmin)
                <li>
                    <a class="btn btn--ghost {{ request()->routeIs('admin.dashboard') ? 'is-active' : '' }}" href="{{ route('admin.dashboard') }}" {{ request()->routeIs('admin.dashboard') ? 'aria-current=page' : '' }}>Overview</a>
                </li>
            @else
                <li>
                    <a class="{{ request()->routeIs('dashboard') ? 'is-active' : '' }}" href="{{ route('dashboard') }}" {{ request()->routeIs('dashboard') ? 'aria-current=page' : '' }}>Dashboard</a>
                </li>
            @endif
            @if($isAdmin)
                <li>
                    <a class="btn btn--ghost {{ ($res==='users') ? 'is-active' : '' }}" href="{{ route('admin.resource.index', ['users']) }}" {{ ($res==='users') ? 'aria-current=page' : '' }}>Users</a>
                </li>
                <li>
                    <a class="btn btn--ghost {{ ($res==='patients') ? 'is-active' : '' }}" href="{{ route('admin.resource.index', ['patients']) }}" {{ ($res==='patients') ? 'aria-current=page' : '' }}>Patients</a>
                </li>
                <li>
                    <a class="btn btn--ghost {{ ($res==='doctors') ? 'is-active' : '' }}" href="{{ route('admin.resource.index', ['doctors']) }}" {{ ($res==='doctors') ? 'aria-current=page' : '' }}>Doctors</a>
                </li>
                <li>
                    <a class="btn btn--ghost {{ ($res==='vitals') ? 'is-active' : '' }}" href="{{ route('admin.resource.index', ['vitals']) }}" {{ ($res==='vitals') ? 'aria-current=page' : '' }}>Vitals</a>
                </li>
            @else
                <li>
                    <a class="{{ request()->routeIs('doctors.*') || request()->routeIs('doctors.index') ? 'is-active' : '' }}" href="{{ route('doctors.index') }}" {{ (request()->routeIs('doctors.*') || request()->routeIs('doctors.index')) ? 'aria-current=page' : '' }}>Doctors</a>
                </li>
                <li>
                    <a class="{{ request()->routeIs('notifications.*') || request()->routeIs('notifications.index') ? 'is-active' : '' }}" href="{{ route('notifications.index') }}" {{ (request()->routeIs('notifications.*') || request()->routeIs('notifications.index')) ? 'aria-current=page' : '' }}>Notifications</a>
                </li>
            @endif
            <li>
                <button id="themeToggle" class="btn btn--ghost" aria-pressed="false" aria-label="Toggle dark mode">🌙</button>
            </li>
            <li>
                <div class="avatar-menu">
                    <button class="avatar" aria-haspopup="menu" aria-expanded="false" aria-label="Open user menu">
                        @php($fallback = 'https://api.dicebear.com/7.x/miniavs/svg?seed=' . urlencode(Auth::user()->name ?? 'User'))
                        <img src="{{ Auth::user()->photo_url ?? $fallback }}" alt="Profile" />
                    </button>
                    <ul class="menu" role="menu">
                        <li role="menuitem"><a class="link" href="{{ route('profile.show') }}">Profile</a></li>
                        <li role="menuitem"><a class="link" href="{{ route('settings.index') }}">Settings</a></li>
                        <li role="menuitem">
                            <form method="POST" action="{{ route('logout') }}">@csrf
                                <button type="submit" class="link-btn">Logout</button>
                            </form>
                        </li>
                    </ul>
                </div>
            </li>
        </ul>
    </nav>
</header>
