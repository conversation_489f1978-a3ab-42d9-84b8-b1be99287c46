<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>Profile – Healthcare Tracker</title>
    @vite(['resources/css/app.css','resources/js/app.js'])
    <style>
        .profile-grid{display:grid;grid-template-columns:320px 1fr;gap:1rem;align-items:start}
        @media (max-width: 920px){.profile-grid{grid-template-columns:1fr}}
        .avatar-lg{width:120px;height:120px;border-radius:50%;object-fit:cover;border:2px solid rgba(0,0,0,.08)}
        .field{display:flex;flex-direction:column;gap:.35rem}
        .field>label{font-size:.8rem;font-weight:600;color:var(--muted,#64748b)}
        .row{display:grid;grid-template-columns:repeat(auto-fit,minmax(220px,1fr));gap:.75rem 1rem}
        .divider{height:1px;background:rgba(0,0,0,.08);margin:1rem 0}
        .dark .divider{background:#1e293b}
        .alert{padding:.75rem 1rem;border-radius:.65rem;background:#ecfeff;color:#0e7490;border:1px solid #a5f3fc}
        .dark .alert{background:#0c4a6e;color:#e0f2fe;border-color:#082f49}
    </style>
</head>
<body class="antialiased font-sans bg-base text-base-text dark:bg-dark-base dark:text-dark-text">
    @include('partials.navbar')

    <main class="section">
        @if (session('status'))
            <div class="alert" role="status">{{ session('status') }}</div>
        @endif

        @if ($errors->any())
            <div class="alert" style="background:#fef2f2;color:#991b1b;border-color:#fecaca">
                <strong>We found some issues:</strong>
                <ul style="margin:.5rem 0 0 .95rem">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <div class="profile-grid">
            <section class="feature-card">
                <h2 style="margin:0 0 .75rem">Profile Photo</h2>
                <div style="display:flex;align-items:center;gap:1rem;flex-wrap:wrap">
                    @php($fallback = 'https://api.dicebear.com/7.x/miniavs/svg?seed=' . urlencode($user->name ?? 'User'))
                    <img id="avatarPreview" class="avatar-lg" src="{{ $user->photo_url ?? $fallback }}" alt="Avatar" />
                    <div>
                        <form method="POST" action="{{ route('profile.update.photo') }}" enctype="multipart/form-data">
                            @csrf
                            
                            <input type="file" name="photo" accept="image/*" onchange="previewAvatar(event)" />
                            <p class="op-70" style="margin:.35rem 0 0;font-size:.85rem">JPG, PNG, GIF, WEBP up to 4MB.</p>
                            <div style="margin-top:.75rem">
                                <button type="submit" class="btn btn--primary-solid">Upload</button>
                            </div>
                        </form>
                    </div>
                </div>
            </section>

            <section class="feature-card">
                <h2 style="margin:0 0 .75rem">Account</h2>
                <form method="POST" action="{{ route('profile.update.account') }}">
                    @csrf
                    @method('PUT')
                    <div class="row">
                        <div class="field">
                            <label for="name">Name</label>
                            <input id="name" name="name" type="text" value="{{ old('name', $user->name) }}" required />
                        </div>
                        <div class="field">
                            <label>Email</label>
                            <div style="padding:.6rem .85rem; border:1px dashed rgba(0,0,0,.15); border-radius:.5rem; background:rgba(0,0,0,.02);" class="dark:border-[rgba(255,255,255,.1)] dark:bg-[rgba(255,255,255,.03)]">
                                {{ $user->email }}
                            </div>
                            <small class="op-70">Email is fixed and cannot be changed.</small>
                        </div>
                    </div>
                    <div style="margin-top:1rem"><button type="submit" class="btn btn--primary-solid">Save changes</button></div>
                </form>
            </section>
        </div>

        @if ($user->role === 'patient')
        <div class="feature-card" style="margin-top:1rem">
            <h2 style="margin:0 0 .75rem">Patient Details</h2>
            <form method="POST" action="{{ route('profile.update.patient') }}">
                @csrf
                @method('PUT')
                <div class="row">
                    <div class="field"><label for="date_of_birth">Date of birth</label><input id="date_of_birth" name="date_of_birth" type="date" value="{{ old('date_of_birth', optional($patient)->date_of_birth) }}" /></div>
                    <div class="field"><label for="gender">Gender</label>
                        <select id="gender" name="gender" style="padding:.6rem .85rem">
                            @php($genders=['male'=>'Male','female'=>'Female','other'=>'Other'])
                            <option value="">Select…</option>
                            @foreach($genders as $k=>$label)
                                <option value="{{ $k }}" @selected(old('gender', optional($patient)->gender) === $k)>{{ $label }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="field"><label for="phone_number">Phone</label><input id="phone_number" name="phone_number" type="text" value="{{ old('phone_number', optional($patient)->phone_number) }}" /></div>
                    <div class="field"><label for="emergency_contact">Emergency contact</label><input id="emergency_contact" name="emergency_contact" type="text" value="{{ old('emergency_contact', optional($patient)->emergency_contact) }}" /></div>
                    <div class="field full"><label for="address">Address</label><input id="address" name="address" type="text" value="{{ old('address', optional($patient)->address) }}" /></div>
                    <div class="field"><label for="insurance_provider">Insurance</label><input id="insurance_provider" name="insurance_provider" type="text" value="{{ old('insurance_provider', optional($patient)->insurance_provider) }}" /></div>
                    <div class="field"><label for="policy_number">Policy #</label><input id="policy_number" name="policy_number" type="text" value="{{ old('policy_number', optional($patient)->policy_number) }}" /></div>
                    <div class="field full"><label for="health_history">Health history</label><textarea id="health_history" name="health_history" placeholder="Conditions, surgeries, notes…">{{ old('health_history', optional($patient)->health_history) }}</textarea></div>
                    <div class="field"><label for="height_cm">Height (cm)</label><input id="height_cm" name="height_cm" type="number" step="0.1" min="40" max="260" value="{{ old('height_cm', optional($patient)->height_cm) }}" /></div>
                    <div class="field"><label for="allergies">Allergies</label><input id="allergies" name="allergies" type="text" value="{{ old('allergies', optional($patient)->allergies) }}" /></div>
                    <div class="field"><label for="medical_conditions">Medical conditions</label><input id="medical_conditions" name="medical_conditions" type="text" value="{{ old('medical_conditions', optional($patient)->medical_conditions) }}" /></div>
                </div>
                <div style="margin-top:1rem"><button type="submit" class="btn btn--primary-solid">Save patient details</button></div>
            </form>
        </div>

        <div class="feature-card" style="margin-top:1rem">
            <div style="display:flex; align-items:center; gap:.75rem; flex-wrap:wrap;">
                <h2 style="margin:0">Vitals</h2>
                @if(isset($latestVital))
                    <span class="text-sm op-70">Latest: {{ optional($latestVital->recorded_at)->setTimezone('Africa/Tunis')?->format('Y-m-d H:i') }} Africa/Tunis</span>
                @endif
                <form method="GET" action="{{ route('profile.show') }}" class="ml-auto" style="display:flex; gap:.35rem; align-items:center; flex-wrap:wrap;">
                    <select name="range" onchange="this.form.submit()" style="padding:.45rem .65rem">
                        @php($opts=['7d'=>'7 days','30d'=>'30 days','90d'=>'90 days','365d'=>'365 days','all'=>'All','custom'=>'Custom'])
                        @foreach($opts as $k=>$label)
                            <option value="{{ $k }}" @selected(($range ?? '30d') === $k)>{{ $label }}</option>
                        @endforeach
                    </select>
                    @if(($range ?? '') === 'custom')
                        <input type="date" name="from" value="{{ $from ?? '' }}" style="padding:.45rem .65rem;"/>
                        <input type="date" name="to" value="{{ $to ?? '' }}" style="padding:.45rem .65rem;"/>
                    @endif
                    <noscript><button class="btn btn--outline" type="submit">Apply</button></noscript>
                </form>
            </div>
            <div class="divider"></div>
            @if(isset($vitals) && $vitals->count())
                <div style="overflow:auto">
                    <table style="width:100%; border-collapse:collapse; font-size:.92rem;">
                        <thead>
                            <tr>
                                <th style="text-align:left; padding:.5rem; border-bottom:1px solid rgba(0,0,0,.08)">Recorded (Africa/Tunis)</th>
                                <th style="text-align:right; padding:.5rem; border-bottom:1px solid rgba(0,0,0,.08)">BP (mmHg)</th>
                                <th style="text-align:right; padding:.5rem; border-bottom:1px solid rgba(0,0,0,.08)">HR (bpm)</th>
                                <th style="text-align:right; padding:.5rem; border-bottom:1px solid rgba(0,0,0,.08)">Temp (°C)</th>
                                <th style="text-align:right; padding:.5rem; border-bottom:1px solid rgba(0,0,0,.08)">SpO₂ (%)</th>
                                <th style="text-align:right; padding:.5rem; border-bottom:1px solid rgba(0,0,0,.08)">Glucose</th>
                                <th style="text-align:right; padding:.5rem; border-bottom:1px solid rgba(0,0,0,.08)">Weight (kg)</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($vitals as $v)
                                <tr>
                                    <td style="padding:.5rem; border-bottom:1px solid rgba(0,0,0,.04)">{{ optional($v->recorded_at)->setTimezone('Africa/Tunis')?->format('Y-m-d H:i') }}</td>
                                    <td style="padding:.5rem; text-align:right; border-bottom:1px solid rgba(0,0,0,.04)">{{ $v->blood_pressure_systolic }}/{{ $v->blood_pressure_diastolic }}</td>
                                    <td style="padding:.5rem; text-align:right; border-bottom:1px solid rgba(0,0,0,.04)">{{ $v->heart_rate }}</td>
                                    <td style="padding:.5rem; text-align:right; border-bottom:1px solid rgba(0,0,0,.04)">{{ number_format((float)$v->temperature, 1) }}</td>
                                    <td style="padding:.5rem; text-align:right; border-bottom:1px solid rgba(0,0,0,.04)">{{ $v->spo2_level }}</td>
                                    <td style="padding:.5rem; text-align:right; border-bottom:1px solid rgba(0,0,0,.04)">{{ number_format((float)$v->glucose_level, 1) }}</td>
                                    <td style="padding:.5rem; text-align:right; border-bottom:1px solid rgba(0,0,0,.04)">{{ number_format((float)$v->weight, 1) }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                <div style="margin-top:.75rem">{{ $vitals->links() }}</div>
            @else
                <p class="op-70">No vitals found for the selected period.</p>
            @endif
        </div>
        @endif
    </main>

    <script>
        function previewAvatar(e){
            const f = e.target.files?.[0];
            if(!f) return;
            const reader = new FileReader();
            reader.onload = () => {
                const img = document.getElementById('avatarPreview');
                if(img) img.src = reader.result;
            };
            reader.readAsDataURL(f);
        }
    </script>
</body>
</html>
