<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\DashboardController;

// JWT Auth endpoints
Route::post('/v1/auth/login', [AuthController::class, 'login']);

Route::middleware('jwt')->group(function(){
    Route::get('/v1/auth/me', [AuthController::class, 'me']);
    Route::post('/v1/auth/refresh', [AuthController::class, 'refresh']);
    Route::post('/v1/auth/logout', [AuthController::class, 'logout']);
    // Protected vitals feed
    Route::get('/v1/vitals/feed', [DashboardController::class, 'feed']);
});
