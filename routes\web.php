<?php
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\InitialVitalController;
use App\Http\Controllers\OnboardingController;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use Illuminate\Http\Request;

Route::get('/', function () {
    return view('welcome');
});

// Password reset via email code (guest only)
Route::middleware('guest')->group(function () {
    Route::get('/password/forgot', [\App\Http\Controllers\PasswordResetController::class, 'showForgotForm'])->name('password.forgot');
    Route::post('/password/forgot', [\App\Http\Controllers\PasswordResetController::class, 'sendCode'])->name('password.forgot.send');
    // Legacy combined form (still available)
    Route::get('/password/reset', [\App\Http\Controllers\PasswordResetController::class, 'showResetForm'])->name('password.reset');
    Route::post('/password/reset', [\App\Http\Controllers\PasswordResetController::class, 'reset'])->name('password.reset.post');
    // New two-step: verify code then set new password
    Route::get('/password/verify', [\App\Http\Controllers\PasswordResetController::class, 'showVerifyForm'])->name('password.verify');
    Route::post('/password/verify', [\App\Http\Controllers\PasswordResetController::class, 'verify'])->name('password.verify.post');
    Route::get('/password/new', [\App\Http\Controllers\PasswordResetController::class, 'showNewPasswordForm'])->name('password.new');
    Route::post('/password/new', [\App\Http\Controllers\PasswordResetController::class, 'setNewPassword'])->name('password.new.post');
    Route::get('/password/success', [\App\Http\Controllers\PasswordResetController::class, 'success'])->name('password.success');
});

// Authentication (basic custom controller)
Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
Route::post('/login', [AuthController::class, 'login'])->middleware('validate.login')->name('login.attempt');
Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
Route::post('/register', [AuthController::class, 'register'])->middleware('validate.register')->name('register.store');
Route::post('/logout', [AuthController::class, 'logout'])->middleware('auth')->name('logout');

// OTP email verification routes
Route::middleware(['auth'])->group(function() {
    Route::get('/email/verify', [AuthController::class, 'showOtpForm'])->name('verification.otp.form');
    Route::post('/email/verify', [AuthController::class, 'verifyOtp'])->name('verification.otp.verify');
    Route::post('/email/verification-resend', [AuthController::class, 'resendOtp'])->name('verification.otp.resend');
    // Basic profile accessible during onboarding as well
    Route::get('/profile', [\App\Http\Controllers\ProfileController::class, 'show'])->name('profile.show');
    // Split update endpoints
    Route::put('/profile/account', [\App\Http\Controllers\ProfileController::class, 'updateAccount'])->name('profile.update.account');
    Route::put('/profile/patient', [\App\Http\Controllers\ProfileController::class, 'updatePatient'])->name('profile.update.patient');
    Route::post('/profile/photo', [\App\Http\Controllers\ProfileController::class, 'updatePhoto'])->name('profile.update.photo');
});

// Onboarding (multi-step flow) and optional initial vitals
Route::middleware(['auth','verified'])->group(function () {
    Route::get('/onboarding', [OnboardingController::class, 'show'])->name('onboarding.show');
    Route::post('/onboarding', [OnboardingController::class, 'store'])->middleware('validate.vitals')->name('onboarding.store');
    // Legacy welcome-onboarding now redirects to the guided flow (preserve route names)
    Route::get('/welcome-onboarding', function(){ return redirect()->route('onboarding.show'); })
        ->name('onboarding.welcome');
    Route::post('/welcome-onboarding', function(){ return redirect()->route('onboarding.show'); })
        ->name('onboarding.welcome.store');
    // Allow a single-session skip
    Route::post('/onboarding/skip', function(){ session(['onboarding_skip_once' => true]); return redirect()->route('dashboard'); })->name('onboarding.skip');
});

Route::get('/dashboard', [\App\Http\Controllers\DashboardController::class, 'index'])
    ->middleware(['auth', 'verified', 'onboarding.required'])->name('dashboard');

// Authenticated app pages
Route::middleware(['auth', 'verified', 'onboarding.required'])->group(function () {
    Route::get('/doctors', [\App\Http\Controllers\DoctorsController::class, 'index'])->name('doctors.index');
    Route::get('/notifications', [\App\Http\Controllers\NotificationsController::class, 'index'])->name('notifications.index');
    Route::get('/settings', [\App\Http\Controllers\SettingsController::class, 'index'])->name('settings.index');
    // Dashboard data feed and export
    Route::get('/vitals/feed', [\App\Http\Controllers\DashboardController::class, 'feed'])->name('vitals.feed');
    Route::get('/dashboard/export/csv', [\App\Http\Controllers\DashboardController::class, 'exportCsv'])->name('dashboard.export.csv');
});

    // Admin Back Office
    Route::prefix('admin')->name('admin.')->middleware(['auth','verified','onboarding.required','admin'])->group(function(){
        Route::get('/', [\App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');

        // Generic CRUD for core resources
        Route::get('/{resource}', [\App\Http\Controllers\Admin\ResourceController::class, 'index'])->name('resource.index');
        Route::get('/{resource}/create', [\App\Http\Controllers\Admin\ResourceController::class, 'create'])->name('resource.create');
        Route::post('/{resource}', [\App\Http\Controllers\Admin\ResourceController::class, 'store'])->name('resource.store');
        Route::get('/{resource}/{id}/edit', [\App\Http\Controllers\Admin\ResourceController::class, 'edit'])->name('resource.edit');
        Route::put('/{resource}/{id}', [\App\Http\Controllers\Admin\ResourceController::class, 'update'])->name('resource.update');
        Route::delete('/{resource}/{id}', [\App\Http\Controllers\Admin\ResourceController::class, 'destroy'])->name('resource.destroy');
    });
